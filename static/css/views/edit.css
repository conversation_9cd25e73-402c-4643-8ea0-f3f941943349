/* ==========================================================================
   Edit Anime & Episode View Styles
   ========================================================================== */

/* --- Main Edit Form --- */

#edit-anime-form input::placeholder {
    text-align: left;
    color: #aaa;
}

/* --- Input with Icon (e.g., for search buttons) --- */

.input-with-icon {
    display: flex;
    align-items: stretch;
    flex-grow: 1;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-color);
    overflow: hidden;
}

.input-with-icon.disabled {
    background-color: #f5f7fa;
    cursor: not-allowed;
}

.input-with-icon input {
    flex-grow: 1;
    border: none;
    outline: none;
    background: transparent;
    padding: 10px;
    font-size: 16px;
}

.input-with-icon .icon-btn {
    flex-shrink: 0;
    width: 38px;
    border: none;
    border-left: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    transition: background-color 0.2s;
}
.input-with-icon .full-width-btn {
    flex-grow: 1;
    border: none;
    background-color: var(--secondary-color);
    cursor: pointer;
    font-size: 16px;
    padding: 10px;
    transition: background-color 0.2s;
    text-align: center;
}

.input-with-icon .icon-btn:hover {
    background-color: #e9ecef;
}

.input-with-icon .icon-btn:disabled {
    background-color: #f5f7fa;
    cursor: not-allowed;
}

.input-with-icon .icon-btn:disabled:hover {
    background-color: #f5f7fa;
}

/* --- Input with Apply Button (for suggested aliases) --- */

.input-with-apply-icon {
    display: flex;
    align-items: stretch;
    flex-grow: 1;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-color);
    overflow: hidden;
}

.input-with-apply-icon input {
    flex-grow: 1;
    border: none;
    outline: none;
    background: transparent;
    padding: 10px;
    font-size: 16px;
}

.input-with-apply-icon .apply-btn {
    flex-shrink: 0;
    width: 38px;
    border: none;
    border-left: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    padding: 0;
    transition: background-color 0.2s;
    color: var(--primary-color);
}


/* ==========================================================================
   Metadata Search Views (TMDB, Bangumi, etc.)
   ========================================================================== */

.metadata-search-list li {
    /* Fixed height for consistent layout (poster 56px + padding 2*10px) */
    height: 78px;
    box-sizing: border-box;
}

.metadata-search-list li .info {
    /* Make info block same height as poster and use flexbox */
    height: 56px;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* Pushes content to top and bottom */
}

.metadata-search-list li .info .title-container {
    display: flex;
    align-items: baseline; /* Align title and ID tag nicely */
}

.metadata-search-list li .info .title-container .title {
    margin: 0;
    padding: 0;
    border: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
}

.id-tag {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 0.9em;
    margin-right: 10px;
    flex-shrink: 0;
}

.metadata-search-list li .info .meta {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.9em;
    color: #666;
    margin: 0;
}

.metadata-search-list li .select-btn {
    flex-shrink: 0;
}


/* ==========================================================================
   TMDB Episode Group (EGID) View Styles
   ========================================================================== */

.egid-group-list li {
    /* Revert to center alignment to keep buttons vertically aligned */
    align-items: center;
}

.egid-group-list .info {
    /* Allow the info block to shrink and not push the buttons */
    min-width: 0;
}

.egid-group-list .meta {
    /* Allow the description to wrap */
    white-space: normal;
    word-break: break-word;
    overflow: visible;
    text-overflow: clip;
}

.egid-group-list .actions {
    /* Ensure buttons don't shrink */
    flex-shrink: 0;
    margin-left: 15px;
}


/* ==========================================================================
   Reassociate Sources View Styles
   ========================================================================== */

#reassociate-target-table tbody tr.selected {
    background-color: #e9f5ff; /* A light blue to indicate selection */
    border-left: 3px solid var(--primary-color);
}
