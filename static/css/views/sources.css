.source-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.source-controls h2 {
    margin: 0;
    padding: 0;
    border: none;
    font-size: 1.5em;
}

.view-description {
    font-size: 0.9em;
    color: #606266;
    background-color: var(--secondary-color);
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    line-height: 1.6;
}

.source-actions button {
    margin-left: 10px;
    background-color: #6c757d;
}

#save-danmaku-sources-btn,
#save-metadata-sources-btn {
    background-color: var(--success-color);
}

#danmaku-sources-list,
#metadata-sources-list {
    list-style: none;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

#danmaku-sources-list li,
#metadata-sources-list li {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s;
    gap: 10px;
}

.aux-search-checkbox {
    flex-shrink: 0;
    width: 18px;
    height: 18px;
}

#danmaku-sources-list li:last-child,
#metadata-sources-list li:last-child {
    border-bottom: none;
}

.source-name {
    flex-grow: 1;
}

.config-btn {
    margin-left: auto;
    flex-shrink: 0;
}

#danmaku-sources-list li:hover,
#metadata-sources-list li:hover {
    background-color: #f8f9fa;
}

#danmaku-sources-list li.selected,
#metadata-sources-list li.selected {
    background-color: #e9ecef;
    border-left: 3px solid var(--primary-color);
}

.status-icon {
    font-size: 1.2em;
    padding: 3px 8px;
    border-radius: 12px;
    background-color: #e9ecef;
    flex-shrink: 0;
}

.source-login-status, .source-status-text {
    font-size: 0.85em;
    color: #6c757d;
    margin-left: 10px;

}

/* Bilibili 状态显示在源列表中的样式 */
#bili-status-on-source-list {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 10px; /* 与源名称保持间距 */
    font-size: 0.9em;
    color: #6c757d;
}

.bili-list-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
}

.bili-list-uname {
    font-weight: 500;
    color: var(--text-color);
}

.bili-list-vip {
    font-size: 0.8em;
    font-weight: bold;
    color: #fb7299; /* Bilibili Pink */
    background-color: rgba(251, 114, 153, 0.1);
    padding: 2px 5px;
    border-radius: 4px;
}

.bili-list-vip.annual {
    color: #ff6600; /* 年度大会员使用不同颜色 */
    background-color: rgba(255, 102, 0, 0.1);
}
