#library-table .poster-cell img {
    width: 50px;
    height: 70px;
    object-fit: cover;
    border-radius: 4px;
    background-color: #eee;
}

.anime-detail-header-main {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-grow: 1;
}

.anime-detail-header-main img {
    width: 100px;
    height: 140px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
}

.episode-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.episode-list-header .header-actions {
    display: flex;
    gap: 10px;
}


.episode-list-actions {
    display: flex;
    justify-content: space-between; /* 保持左右对齐 */
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.episode-list-actions .actions-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.episode-list-actions .header-actions {
    display: flex;
    gap: 10px;
}

/* 确保“全选”按钮在切换文本时保持宽度一致 */
#select-all-episodes-btn {
    min-width: 80px;
    text-align: center;
}

.sort-switch-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 10px; /* Add some space from the previous button */
}

.sort-switch-container label {
    font-size: 14px;
    color: #606266;
    cursor: pointer;
}

/* The switch - a box around the slider */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
  -webkit-transform: translateX(22px);
  -ms-transform: translateX(22px);
  transform: translateX(22px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.episode-list-header h3 {
    margin: 0;
}

#danmaku-list-view pre {
    flex-grow: 1;
    overflow-y: auto;
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
    padding: 10px;
    white-space: pre-wrap;
    word-break: break-all;
}

.bulk-actions-bar {
    display: flex;
    justify-content: flex-start; /* 将按钮组推到左边 */
    align-items: center;
    gap: 10px; /* 按钮之间的间距 */
    margin-bottom: 10px;
}

#source-detail-table th:first-child,
#source-detail-table td:first-child {
    width: 1%;
    padding-left: 20px; /* Match table header padding */
    }

    /* Status column for source list */
    #source-detail-table th:nth-child(4),
    #source-detail-table td:nth-child(4) {
        width: 8%;
        text-align: center;
    }

    #source-detail-table .status-cell {
        font-size: 1.2em;
        white-space: nowrap; /* Prevent icons from wrapping */
}

.egid-detail-list {
    list-style: none;
    padding: 0;
}

.egid-detail-list .season-header {
    font-size: 1.2em;
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
}

.egid-detail-list .episode-item {
    padding: 5px 0;
}

.action-buttons-wrapper .action-btn {
    border-radius: 6px; /* 使用固定的圆角值，形成圆角方形 */
    width: 30px;
    height: 30px;
    padding: 0; /* 重置内边距以确保尺寸精确 */
    display: flex; /* 使用 flexbox 居中图标 */
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease; /* 添加过渡效果 */
}

.action-buttons-wrapper .action-btn:hover {
    background-color: var(--secondary-color); /* 悬停时显示浅灰色背景 */
}

/* Style for the disabled state of the schedule icon */
.action-buttons-wrapper .action-btn.disabled-icon {
    opacity: 0.35;
    filter: grayscale(1);
}

#episode-list-table th:first-child,
#episode-list-table td:first-child {
    width: 1%;
}

/* --- Episode List Table Layout --- */
#episode-list-table {
    table-layout: fixed; /* 强制表格遵守设定的宽度 */
    width: 100%;
}

#episode-list-table th,
#episode-list-table td {
    word-wrap: break-word; /* 确保长内容能换行 */
    overflow: hidden;
    text-overflow: ellipsis; /* 超出部分显示省略号 */
}

/* 设置每一列的宽度 */
#episode-list-table th:nth-child(1), #episode-list-table td:nth-child(1) { width: 4%; } /* Checkbox */
#episode-list-table th:nth-child(2), #episode-list-table td:nth-child(2) { width: 8%; } /* ID */
#episode-list-table th:nth-child(3), #episode-list-table td:nth-child(3) { width: 30%; } /* 剧集名 - Reduced width */
#episode-list-table th:nth-child(4), #episode-list-table td:nth-child(4) { width: 8%; text-align: center; } /* 集数 */
#episode-list-table th:nth-child(5), #episode-list-table td:nth-child(5) { width: 10%; text-align: center; } /* 弹幕数 */
#episode-list-table th:nth-child(6), #episode-list-table td:nth-child(6) { width: 15%; } /* 采集时间 */
#episode-list-table th:nth-child(7), #episode-list-table td:nth-child(7) { width: 8%; text-align: center; } /* 官方链接 */
#episode-list-table th:nth-child(8), #episode-list-table td:nth-child(8) { width: 17%; } /* 剧集操作 - Increased width */

/* 官方链接表头换行 */
#episode-list-table th:nth-child(7) {
    line-height: 1.2; /* 调整行高以适应两行文本 */
}
