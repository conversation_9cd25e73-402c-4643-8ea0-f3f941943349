#task-list {
    list-style: none;
    padding: 0;
}

.task-item {
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    border-left: 5px solid var(--primary-color);
    transition: background-color 0.2s, border-left-color 0.2s;
}

.task-item:hover {
    background-color: #f8f9fa;
}

.task-item[data-status="排队中"] {
    border-left-color: #909399;
}
.task-item[data-status="已完成"] {
    border-left-color: var(--success-color);
}
.task-item[data-status="失败"] {
    border-left-color: var(--error-color);
}

.task-item.selected {
    background-color: #e9ecef;
    border-left-color: #0056b3; /* A darker blue to indicate selection */
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    margin-bottom: 10px;
}

.task-status {
    font-size: 0.9em;
    padding: 3px 8px;
    border-radius: 12px;
    background-color: #e9ecef;
}

.task-progress-bar-container {
    width: 100%;
    background-color: #e9ecef;
    border-radius: 4px;
    height: 8px;
    overflow: hidden;
}

.task-progress-bar {
    height: 100%;
    width: 0%;
    background-color: var(--primary-color);
    border-radius: 4px;
    transition: width 0.3s ease-in-out;
}

.library-header .view-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.view-controls {
    width: 100%;
    justify-content: space-between;
}

.view-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.view-controls input[type="text"] {
    max-width: 300px;
}

.view-controls .task-actions {
    display: flex;
    gap: 10px;
}

.view-controls .control-btn {
    padding: 5px 12px;
    border: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    color: #666;
    cursor: pointer;
    border-radius: 15px;
    font-size: 14px;
    min-width: 90px; /* 设置最小宽度以保持一致 */
    text-align: center; /* 确保文本居中 */
    box-sizing: border-box; /* 确保 padding 和 border 包含在宽度内 */
}

.view-controls .control-btn.danger {
    background-color: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.view-controls .control-btn.danger:hover {
    background-color: #d43f3f;
}

.view-controls .control-btn:disabled {
    background-color: #e9ecef;
    color: #c0c4cc;
    cursor: not-allowed;
    border-color: #e9ecef;
}
