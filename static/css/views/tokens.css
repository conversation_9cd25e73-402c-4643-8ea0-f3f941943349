.token-value {
    font-family: 'Courier New', Courier, monospace;
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.token-visibility-toggle {
    cursor: pointer;
    font-size: 1.1em;
}

.token-status.disabled {
    color: #909399;
    font-style: italic;
}

.date-cell {
    line-height: 1.2;
    font-size: 0.9em;
}

.date-cell .time-part {
    color: #666;
}

#token-table {
    table-layout: fixed;
    width: 100%;
}

#token-table th, #token-table td {
    word-wrap: break-word;
}

#token-table th:nth-child(1),
#token-table td:nth-child(1) {
    width: 8%; /* 名称 (Name) */
}

#token-table th:nth-child(2),
#token-table td:nth-child(2) {
    width: 23%; /* Token - 增加宽度以容纳长字符串 */
}

#token-table th:nth-child(3),
#token-table td:nth-child(3) {
    width: 8%;
    text-align: center;
}

#token-table th:nth-child(4), #token-table td:nth-child(4),
#token-table th:nth-child(5), #token-table td:nth-child(5) {
    width: 10%; /* 创建时间 & 有效期 (Created & Expires) */
}

.ua-filter-section {
    margin-top: 20px;
    padding: 20px;
    background: var(--secondary-color);
    border-radius: 8px;
}

.domain-config-section {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--secondary-color);
    border-radius: 8px;
}

.domain-config-section h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.domain-config-section .form-row {
    align-items: center;
}

.domain-config-section button {
    margin-left: 10px;
}

.token-name-cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
