/* Generic Components */
.content-view > div:not(.form-card):not(.view-header-flexible):not(.view-header-with-actions) {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.library-header, .section-header-with-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.library-header h2, .section-header-with-action h2 {
    border: none;
    padding: 0;
    margin: 0;
}

.view-header-flexible {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.view-header-with-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
}

.view-header-with-actions h2 {
    margin: 0;
    padding: 0;
    border: none;
}

.form-card {
    max-width: 700px;
    margin: 20px auto;
    padding: 20px;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.form-card h2, .form-card h3 {
    border-bottom: none;
    padding-bottom: 0;
    margin: 0 0 10px 0;
}

.form-row {
    display: flex;
    align-items: center;
    gap: 15px;
}

.form-row label {
    width: 120px;
    text-align: right;
    flex-shrink: 0;
    font-weight: 500;
    color: #606266;
}

.form-row input, .form-row select {
    flex-grow: 1;
}

.form-row input[type="checkbox"] {
    width: auto;
    flex-grow: 0;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

input[type="text"],
input[type="password"],
input[type="number"],
select {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
}

input:disabled {
    background-color: #f5f7fa;
    color: #c0c4cc;
    cursor: not-allowed;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: white;
    font-size: 16px;
    cursor: pointer;
    /* 增加浏览器前缀以提高兼容性，并防止意外的文本选择 */
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none;    /* Firefox */
    user-select: none;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #357abd;
}

button:disabled {
    background-color: #a0c7e4;
    cursor: not-allowed;
}

#logout-btn {
    background-color: var(--error-color);
    padding: 8px 12px;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
    padding: 5px;
}

.message {
    text-align: center;
    margin-top: 10px;
    font-size: 0.9em;
    min-height: 1.2em;
}

.message.success { color: var(--success-color); }
.message.error { color: var(--error-color); }

#loader {
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px 20px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

th {
    background-color: var(--secondary-color);
    font-weight: 600;
}

.actions-cell {
    /* 移除右对齐，让内容自然左对齐 */
}

.actions-cell .action-buttons-wrapper {
    display: flex; /* 改为 flex 以便更好地控制内部元素 */
    align-items: center;
    gap: 8px;
}

.secondary-btn {
    background-color: #6c757d;
}

.secondary-btn:hover {
    background-color: #5a6268;
}

/* --- Modal Styles --- */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-color);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    width: 90%;
    max-width: 600px;
    max-height: 85vh; /* 限制弹窗高度，避免超出视口 */
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.modal-header h3 {
    margin: 0;
    border: none;
    padding: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body .form-row {
    margin-bottom: 15px;
}

/* 让模态内容可滚动，头部/底部固定 */
.modal-body {
    overflow: auto;
    flex: 1 1 auto;
    min-height: 0; /* 防止在flex容器中溢出 */
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
    margin-top: 20px;
}

.modal-help-text {
    font-size: 0.9em;
    color: #606266;
    margin-bottom: 15px;
    background-color: var(--secondary-color);
    padding: 10px;
    border-radius: 4px;
    line-height: 1.5;
}

.modal-help-text a {
    color: var(--primary-color);
    text-decoration: none;
}

.modal-help-text a:hover {
    text-decoration: underline;
}

/* 紧凑表格：用于模态中的预览表格 */
.compact-table th,
.compact-table td {
    padding: 8px 12px;
}

/* Bilibili Login Modal Styles */
#bili-login-section {
    margin-top: 20px;
    padding-top: 15px;
    padding-bottom: 15px; /* 增加底部间距，与顶部对称 */
    text-align: center; /* 确保内部文本居中 */
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

#bili-user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 5px;
}

#bili-user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
}

#bili-user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

#bili-user-vip-status.vip {
    font-size: 0.8em;
    font-weight: bold;
    color: #fb7299; /* Bilibili Pink */
    background-color: rgba(251, 114, 153, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    margin-top: 4px;
}

#bili-login-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

#bili-qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px; /* 二维码和刷新按钮之间的间距 */
}

#bili-qrcode-container img,
#bili-qrcode-container canvas {
    width: 180px;
    height: 180px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

#bili-login-status {
    font-size: 0.9em;
    color: #606266;
    min-height: 1.2em;
}

#bili-login-status.success {
    color: var(--success-color);
    font-weight: bold;
}

#bili-login-status.error {
    color: var(--error-color);
}

#bili-qrcode-container img.expired {
    opacity: 0.3;
    filter: grayscale(100%);
}

.bili-disclaimer-agreement {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 15px;
    font-size: 14px;
}

.bili-disclaimer-agreement label {
    cursor: pointer;
    color: #606266;
}

.bili-disclaimer-agreement input[type="checkbox"] {
    cursor: pointer;
    width: 16px;
    height: 16px;
    margin: 0;
}

.bili-login-disclaimer {
    font-size: 12px;
    color: #909399; /* A muted grey color */
    margin-top: 10px;
    padding: 0 10px;
    line-height: 1.4;
    text-align: center;
}

.bili-login-disclaimer a {
    color: var(--primary-color);
    text-decoration: none;
}

.bili-login-disclaimer a:hover {
    text-decoration: underline;
}

