:root {
  /* 毛玻璃主题色彩 - 默认模式 */
  --primary: #6366f1;
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;
  --accent: #06b6d4;
  --accent-light: #22d3ee;
  --accent-dark: #0891b2;
  
  /* 背景渐变 */
  --bg-gradient: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  --bg-overlay: rgba(255, 255, 255, 0.1);
  
  /* 毛玻璃效果 */
  --glass-bg: rgba(255, 255, 255, 0.7);
  --glass-border: rgba(255, 255, 255, 0.8);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  --glass-backdrop: blur(16px) saturate(180%);
  
  /* 文字颜色 - 深色文字提高可读性 */
  --text: #1f2937;
  --text-secondary: rgba(31, 41, 55, 0.8);
  --text-muted: rgba(31, 41, 55, 0.6);
  --text-contrast: #ffffff;
  --text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
  
  /* 状态颜色 */
  --success: #059669;
  --error: #dc2626;
  --warning: #d97706;
  
  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
}

/* 深色模式 */
[data-theme="dark"] {
 /* 统一深色模式配色（所有页面一致） */
 --bg-gradient: linear-gradient(135deg, #0f172a 0%, #111827 100%);
 --bg-overlay: rgba(0, 0, 0, 0.3);
 --glass-bg: rgba(17, 24, 39, 0.82);
 --glass-border: rgba(255, 255, 255, 0.08);
 --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
 --text: #ffffff;
 --text-secondary: rgba(255, 255, 255, 0.9);
 --text-muted: rgba(255, 255, 255, 0.7);
 --text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 深色模式：统一主要容器外观，避免不同页面出现不同深浅 */
[data-theme="dark"] .card,
[data-theme="dark"] .auth-card,
[data-theme="dark"] .topbar,
[data-theme="dark"] .bottom-nav {
  background: var(--glass-bg) !important;
  border-color: var(--glass-border) !important;
}

/* 深色模式：确保各页面卡片背景为深色且不被毛玻璃提亮 */
[data-theme="dark"] #search-card,
[data-theme="dark"] #recent-card,
[data-theme="dark"] #results-card,
[data-theme="dark"] #tasks-card,
[data-theme="dark"] #library-card,
[data-theme="dark"] #tokens-card,
[data-theme="dark"] #tokens-ua-card,
[data-theme="dark"] #tokens-log-card,
[data-theme="dark"] #settings-card {
 background: #111827 !important;
 backdrop-filter: none !important;
 -webkit-backdrop-filter: none !important;
}

/* 深色模式：减少固定背景与入场动画导致的闪烁 */
[data-theme="dark"] body { 
 background: var(--bg-gradient) !important;
 background-attachment: scroll;
 min-height: 100vh;
 min-height: 100dvh;
}

/* 确保深色模式下html元素也有深色背景 */
[data-theme="dark"] html {
 background: #0f172a !important;
}

/* 深色模式下确保所有页面内容区域也有深色背景 */
[data-theme="dark"] #app {
 background-color: #0f172a !important;
 min-height: 100vh;
 min-height: 100dvh;
}

[data-theme="dark"] #auth-screen {
 background-color: transparent !important;
}

[data-theme="dark"] #main-screen {
 background-color: transparent !important;
}

[data-theme="dark"] .content {
 background-color: transparent !important;
}

/* 深色模式下确保搜索、结果、任务等所有卡片使用深色背景 */
[data-theme="dark"] #search-card,
[data-theme="dark"] #recent-card,
[data-theme="dark"] #results-card,
[data-theme="dark"] #tasks-card,
[data-theme="dark"] #library-card,
[data-theme="dark"] #tokens-card,
[data-theme="dark"] #tokens-ua-card,
[data-theme="dark"] #tokens-log-card {
 background: rgba(17, 24, 39, 0.82) !important;
 backdrop-filter: blur(16px) saturate(180%) !important;
 -webkit-backdrop-filter: blur(16px) saturate(180%) !important;
 border: 1px solid rgba(255, 255, 255, 0.08) !important;
}
[data-theme="dark"] body::before { 
 position: absolute; 
 opacity: 1; 
}
[data-theme="dark"] .content,
[data-theme="dark"] .topbar,
[data-theme="dark"] .bottom-nav,
[data-theme="dark"] .card { animation: none !important; }
[data-theme="dark"] #token-list li,
[data-theme="dark"] #library-list li,
[data-theme="dark"] #tasks-list li { animation: none !important; transition: none; }

/* 亮色模式 */
[data-theme="light"] {
  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --glass-bg: rgba(255, 255, 255, 0.9);
  --glass-border: rgba(255, 255, 255, 0.95);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  --text: #0f172a;
  --text-secondary: rgba(15, 23, 42, 0.8);
  --text-muted: rgba(15, 23, 42, 0.6);
  --text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

* { 
 box-sizing: border-box; 
}

html, body { 
 height: 100%; 
 max-width: 100%; 
 overflow-x: hidden; 
}

body {
 margin: 0;
 font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
 background: var(--bg-gradient);
 background-attachment: fixed;
 color: var(--text);
 text-shadow: var(--text-shadow);
 /* 使用动态视口高度 */
 min-height: 100vh;
 min-height: 100dvh; /* 动态视口高度，适应移动端 */
 position: relative;
}

/* 确保深色模式下body的背景色 - 先设置纯色再设置渐变 */
[data-theme="dark"] body {
 background-color: #0f172a !important;
 background-image: var(--bg-gradient) !important;
 background-attachment: fixed !important;
}

/* 动态背景粒子效果 */
body::before {
 content: '';
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 background: 
   radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
   radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
   radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
 pointer-events: none;
 z-index: -1;
}

/* 深色模式：调整背景粒子效果 */
[data-theme="dark"] body::before {
 background: 
   radial-gradient(circle at 20% 80%, rgba(30, 30, 80, 0.15) 0%, transparent 50%),
   radial-gradient(circle at 80% 20%, rgba(60, 30, 80, 0.15) 0%, transparent 50%),
   radial-gradient(circle at 40% 40%, rgba(30, 80, 120, 0.1) 0%, transparent 50%);
}

.hidden { display: none !important; }

#app { 
 min-height: 100%; 
 min-height: 100dvh; /* 动态视口高度 */
 display: flex; 
 width: 100%; 
 max-width: 100%; 
 overflow-x: hidden; 
}
#main-screen { 
 width: 100%; 
 min-height: 100vh;
 min-height: 100dvh; /* 确保主屏幕填满动态视口 */
}

/* Auth 登录界面 */
#auth-screen { 
 display: grid; 
 place-items: center; 
 width: 100%; 
 padding: 24px; 
 min-height: 100vh;
 min-height: 100dvh; /* 登录界面也使用动态视口高度 */
 position: relative;
}

.theme-toggle-login {
 position: absolute;
 top: 20px;
 right: 20px;
 z-index: 10;
}

.auth-card { 
 width: 100%; 
 max-width: 400px; 
 background: var(--glass-bg);
 backdrop-filter: var(--glass-backdrop);
 -webkit-backdrop-filter: var(--glass-backdrop);
 border: 1px solid var(--glass-border); 
 border-radius: var(--radius-xl); 
 padding: 32px; 
 box-shadow: var(--glass-shadow);
 position: relative;
 overflow: hidden;
}

.auth-card::before {
 content: '';
 position: absolute;
 top: 0;
 left: 0;
 right: 0;
 height: 1px;
 background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.brand { 
 display: flex; 
 align-items: center; 
 justify-content: center;
 gap: 12px; 
 margin-bottom: 24px; 
}

.brand img { 
 width: 48px; 
 height: 48px; 
 border-radius: var(--radius-md);
 box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.brand h1 { 
 font-size: 24px; 
 font-weight: 600;
 margin: 0; 
 color: var(--text);
 text-shadow: var(--text-shadow);
}

.auth-card form { 
 display: grid; 
 gap: 16px; 
}

.auth-card input { 
 height: 48px; 
 padding: 0 16px; 
 border-radius: var(--radius-md); 
 border: 1px solid var(--glass-border); 
 background: rgba(255, 255, 255, 0.08);
 backdrop-filter: blur(8px);
 font-size: 16px;
 color: var(--text);
 transition: all 0.3s ease;
}

.auth-card input::placeholder {
 color: var(--text-muted);
}

.auth-card input:focus {
 outline: none;
 border-color: var(--accent);
 background: rgba(255, 255, 255, 0.12);
 box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
}

.auth-card .primary { 
 height: 48px; 
 background: linear-gradient(135deg, var(--primary), var(--primary-dark));
 color: #fff; 
 border: none; 
 border-radius: var(--radius-md); 
 font-size: 16px;
 font-weight: 600;
 cursor: pointer;
 transition: all 0.3s ease;
 position: relative;
 overflow: hidden;
}

.auth-card .primary::before {
 content: '';
 position: absolute;
 top: 0;
 left: -100%;
 width: 100%;
 height: 100%;
 background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
 transition: left 0.6s ease;
}

.auth-card .primary:hover::before {
 left: 100%;
}

.auth-card .primary:active {
 transform: translateY(1px);
}

.message.error { 
 color: var(--error); 
 text-align: center; 
 min-height: 20px; 
 font-size: 14px;
 padding: 8px 12px;
 background: rgba(239, 68, 68, 0.1);
 border: 1px solid rgba(239, 68, 68, 0.2);
 border-radius: 24px;
 backdrop-filter: blur(8px);
}

/* Main 主界面 */
.topbar { 
 position: sticky; 
 top: 0; 
 z-index: 10; 
 display: flex; 
 justify-content: space-between; 
 align-items: center; 
 padding: 16px 20px; 
 background: var(--glass-bg);
 backdrop-filter: var(--glass-backdrop);
 -webkit-backdrop-filter: var(--glass-backdrop);
 border-bottom: 1px solid var(--glass-border);
 box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.brand-mini { 
 display: flex; 
 align-items: center; 
 gap: 10px; 
}

.brand-mini img { 
 width: 32px; 
 height: 32px; 
 border-radius: var(--radius-sm);
 box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.brand-mini span { 
 font-weight: 700; 
 font-size: 18px;
 letter-spacing: 0.2px;
 color: var(--text);
 text-shadow: var(--text-shadow);
}

.user { 
 display: flex; 
 align-items: center; 
 gap: 12px; 
 color: var(--text-secondary); 
}

.text-btn { 
 background: rgba(255, 255, 255, 0.1); 
 border: 1px solid var(--glass-border); 
 color: var(--accent-light); 
 font-size: 14px;
 padding: 6px 12px;
 border-radius: var(--radius-sm);
 backdrop-filter: blur(8px);
 transition: all 0.3s ease;
 cursor: pointer;
}

.text-btn:hover {
 background: rgba(255, 255, 255, 0.15);
 transform: translateY(-1px);
}

.content { 
 width: 100%; 
 max-width: 100%; 
 box-sizing: border-box; 
 padding: 0 16px 100px 16px; 
 display: grid; 
 gap: 16px; 
}

/* 确保最近搜索卡片不与搜索框重叠 */
#recent-card {
 margin-top: 8px;
}

.card { 
 width: 100%; 
 background: var(--glass-bg);
 backdrop-filter: var(--glass-backdrop);
 -webkit-backdrop-filter: var(--glass-backdrop);
 border: 1px solid var(--glass-border); 
 border-radius: var(--radius-lg); 
 padding: 20px; 
 box-shadow: var(--glass-shadow);
 position: relative;
 overflow: hidden;
 transition: all 0.3s ease;
}

.card::before {
 content: '';
 position: absolute;
 top: 0;
 left: 0;
 right: 0;
 height: 1px;
 background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.card:hover {
 transform: translateY(-2px);
 box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.search-sticky { 
 position: relative; 
 top: 0; 
 z-index: 10; /* 确保搜索框在上层 */
 margin-bottom: 8px; /* 与“最近搜索”保持间距 */
}

main.content > .card { 
 will-change: transform, opacity; 
}

.search-bar { 
 display: grid; 
 grid-template-columns: 1fr auto; 
 gap: 12px; 
}

.search-bar input { 
 height: 48px; 
 padding: 0 18px; 
 border-radius: 24px; 
 border: 1px solid var(--glass-border); 
 font-size: 16px; 
 background: rgba(255, 255, 255, 0.08);
 backdrop-filter: blur(8px);
 color: var(--text);
 transition: all 0.3s ease;
}

.search-bar input::placeholder {
 color: var(--text-muted);
}

.search-bar input:focus {
 outline: none;
 border-color: var(--accent);
 background: rgba(255, 255, 255, 0.12);
 box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
}

.search-bar .primary { 
 height: 48px; 
 padding: 0 24px; 
 background: linear-gradient(135deg, var(--accent), var(--accent-dark));
 color: #fff; 
 border: none; 
 border-radius: 24px; 
 font-weight: 600;
 font-size: 16px;
 cursor: pointer;
 transition: all 0.3s ease;
 position: relative;
 overflow: visible; /* 改为visible以显示环绕进度条 */
 z-index: 1;
}

/* 搜索按钮进度条容器 */
.search-btn-wrapper {
 position: relative;
 display: inline-flex;
 align-items: center;
 justify-content: center;
}

/* 隐藏旧的 SVG 进度条 */
.search-progress-ring {
 display: none !important;
}

/* 彩虹边框动画 - 使用 box-shadow 实现环绕效果 */
@keyframes rainbow-border {
 0% {
   box-shadow: 
     0 0 0 3px #ff69b4,  /* 粉红 */
     0 0 20px rgba(255, 105, 180, 0.6),
     0 0 40px rgba(255, 105, 180, 0.3);
 }
 16.66% {
   box-shadow: 
     0 0 0 3px #ff8c00,  /* 橙色 */
     0 0 20px rgba(255, 140, 0, 0.6),
     0 0 40px rgba(255, 140, 0, 0.3);
 }
 33.33% {
   box-shadow: 
     0 0 0 3px #ffd700,  /* 黄色 */
     0 0 20px rgba(255, 215, 0, 0.6),
     0 0 40px rgba(255, 215, 0, 0.3);
 }
 50% {
   box-shadow: 
     0 0 0 3px #9370db,  /* 紫色 */
     0 0 20px rgba(147, 112, 219, 0.6),
     0 0 40px rgba(147, 112, 219, 0.3);
 }
 66.66% {
   box-shadow: 
     0 0 0 3px #4169e1,  /* 蓝色 */
     0 0 20px rgba(65, 105, 225, 0.6),
     0 0 40px rgba(65, 105, 225, 0.3);
 }
 83.33% {
   box-shadow: 
     0 0 0 3px #00ced1,  /* 青色 */
     0 0 20px rgba(0, 206, 209, 0.6),
     0 0 40px rgba(0, 206, 209, 0.3);
 }
 100% {
   box-shadow: 
     0 0 0 3px #ff69b4,  /* 回到粉红 */
     0 0 20px rgba(255, 105, 180, 0.6),
     0 0 40px rgba(255, 105, 180, 0.3);
 }
}

/* 搜索中按钮状态 - 应用彩虹边框动画 */
.search-bar .primary.searching {
 background: linear-gradient(135deg, var(--accent-dark), var(--accent));
 cursor: not-allowed;
 position: relative;
 overflow: visible;
 animation: rainbow-border 2s linear infinite;
 transition: all 0.3s ease;
}

/* 搜索完成状态 */
.search-bar .primary.completed {
 background: linear-gradient(135deg, #10b981, #059669);
 animation: success-flash 0.6s ease;
 box-shadow: 
   0 0 0 3px #10b981,
   0 0 20px rgba(16, 185, 129, 0.6),
   0 0 40px rgba(16, 185, 129, 0.3);
}

@keyframes success-flash {
 0% { transform: scale(1); }
 50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
 100% { transform: scale(1); }
}


.search-bar .primary::before {
 content: '';
 position: absolute;
 top: 0;
 left: -100%;
 width: 100%;
 height: 100%;
 background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
 transition: left 0.6s ease;
}

.search-bar .primary:hover::before {
 left: 100%;
}

.search-bar .primary:active { 
 transform: translateY(1px);
}

.loader { 
 width: 32px; 
 height: 32px; 
 border: 3px solid rgba(255, 255, 255, 0.2); 
 border-top-color: var(--accent-light); 
 border-radius: 50%; 
 margin: 12px auto 0; 
 animation: spin 1s linear infinite; 
}

@keyframes spin { 
 to { transform: rotate(360deg); } 
}

/* 精美进度条 - 隐藏传统进度条 */
.progress { 
 display: none; /* 隐藏传统进度条，使用新的环形进度条 */
}

.progress .bar { 
 position: absolute; 
 left: 0; 
 top: 0; 
 bottom: 0; 
 width: 0%; 
 border-radius: 20px; 
 background: linear-gradient(90deg, var(--accent), var(--accent-light), var(--accent));
 box-shadow: 0 0 20px rgba(6, 182, 212, 0.4);
 transition: width .3s ease; 
}

.progress .label { 
 position: absolute; 
 right: 12px; 
 top: 50%; 
 transform: translateY(-50%); 
 font-size: 11px; 
 font-weight: 600;
 color: var(--text);
}

.progress.indeterminate .bar { 
 width: 30%; 
 left: -30%; 
 animation: indet 1.5s ease-in-out infinite; 
}

@keyframes indet { 
 0% { left: -30%; } 
 50% { left: 40%; } 
 100% { left: 100%; } 
}

/* Skeleton 骨架屏 */
.skeleton { 
 display: grid; 
 gap: 16px; 
}

.skeleton::before, .skeleton::after, .skeleton div { 
 content: ""; 
 display: block; 
 height: 68px; 
 border-radius: var(--radius-md); 
 background: linear-gradient(90deg, 
   rgba(255, 255, 255, 0.08) 25%, 
   rgba(255, 255, 255, 0.15) 37%, 
   rgba(255, 255, 255, 0.08) 63%); 
 background-size: 400% 100%; 
 animation: shine 1.5s ease-in-out infinite; 
}

@keyframes shine { 
 0% { background-position: 100% 0 } 
 100% { background-position: 0 0 } 
}

/* Empty 空状态 */
.empty { 
 text-align: center; 
 padding: 24px 16px; 
 color: var(--text-muted); 
}

.empty-illustration { 
 width: 120px; 
 height: 80px; 
 margin: 16px auto; 
 border-radius: var(--radius-md); 
 background: var(--glass-bg);
 backdrop-filter: blur(8px);
 border: 1px solid var(--glass-border);
 box-shadow: var(--glass-shadow);
}

.empty-title { 
 margin: 8px 0 4px; 
 font-weight: 700; 
 font-size: 16px;
 color: var(--text); 
}

.empty-sub { 
 margin: 0 0 16px; 
 color: var(--text-muted);
 font-size: 14px;
}

.empty-actions { 
 display: flex; 
 justify-content: center; 
 gap: 12px; 
}

/* Settings subnav 设置子导航 */
.subnav { 
 display: grid; 
 grid-template-columns: repeat(auto-fit, minmax(88px, 1fr)); 
 gap: 8px; 
 padding: 8px 0 12px; 
}

.subnav-btn { 
 height: 36px; 
 padding: 0 12px; 
 border-radius: var(--radius-md); 
 border: 1px solid var(--glass-border); 
 background: rgba(255, 255, 255, 0.08);
 backdrop-filter: blur(8px);
 color: var(--text-secondary); 
 font-size: 13px; 
 font-weight: 500;
 white-space: nowrap; 
 text-align: center;
 cursor: pointer;
 transition: all 0.3s ease;
}

.subnav-btn:hover {
 background: rgba(255, 255, 255, 0.12);
 transform: translateY(-1px);
}

.subnav-btn.active { 
 background: var(--glass-bg);
 color: var(--accent-light); 
 border-color: var(--accent);
 box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
}
.settings-view.hidden { display: none; }

/* 限制设置内容不会撑破屏幕 */
#settings-card .grid-1, #settings-card .grid-2, #settings-card .grid-3 { max-width: 100%; }
#settings-card .grid-2 { grid-template-columns: minmax(0,1fr) auto; }
#settings-card .grid-3 { grid-template-columns: minmax(0,1fr) auto auto; }

/* 表单元素美化 */
#settings-card input, 
#settings-card textarea, 
#settings-card select { 
 width: 100%; 
 max-width: 100%; 
 min-width: 0; 
 box-sizing: border-box;
 padding: 12px 16px;
 border: 1px solid var(--glass-border);
 border-radius: var(--radius-md);
 background: rgba(255, 255, 255, 0.08);
 backdrop-filter: blur(8px);
 color: var(--text);
 font-size: 14px;
 transition: all 0.3s ease;
}

#settings-card input::placeholder,
#settings-card textarea::placeholder {
 color: var(--text-muted);
}

/* 只读输入框默认隐藏 */
#settings-card input[readonly] {
 display: none;
 opacity: 0;
 transition: opacity 0.3s ease;
}

/* 显示只读输入框 */
#settings-card input[readonly].show {
 display: block;
 opacity: 1;
}

/* Token页面输入框样式与设置页面一致 */
#tokens-card input, 
#tokens-card textarea, 
#tokens-card select { 
 width: 100%; 
 max-width: 100%; 
 min-width: 0; 
 box-sizing: border-box;
 padding: 12px 16px;
 border: 1px solid var(--glass-border);
 border-radius: var(--radius-md);
 background: rgba(255, 255, 255, 0.08);
 backdrop-filter: blur(8px);
 color: var(--text);
 font-size: 14px;
 transition: all 0.3s ease;
}

#tokens-card input::placeholder,
#tokens-card textarea::placeholder {
 color: var(--text-muted);
}

#tokens-card input:focus,
#tokens-card textarea:focus,
#tokens-card select:focus {
 outline: none;
 border-color: var(--accent);
 box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
}

#settings-card input:focus,
#settings-card textarea:focus,
#settings-card select:focus {
 outline: none;
 border-color: var(--accent);
 background: rgba(255, 255, 255, 0.12);
 box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
}

#settings-card input, 
#settings-card textarea { 
 word-break: break-word; 
 overflow-wrap: anywhere; 
}

/* 精美的PPT风格动画效果 */
@keyframes slideInFromLeft { 
 from { 
   opacity: 0; 
   transform: translateX(-20px) scale(0.95); /* 减少位移和缩放以减少跳动 */
 } 
 to { 
   opacity: 1; 
   transform: translateX(0) scale(1); 
 } 
}

@keyframes slideInFromRight { 
 from { 
   opacity: 0; 
   transform: translateX(30px) scale(0.9); 
 } 
 to { 
   opacity: 1; 
   transform: translateX(0) scale(1); 
 } 
}

@keyframes slideInFromBottom { 
 from { 
   opacity: 0; 
   transform: translateY(20px) scale(0.95); 
 } 
 to { 
   opacity: 1; 
   transform: translateY(0) scale(1); 
 } 
}

@keyframes zoomIn { 
 from { 
   opacity: 0; 
   transform: scale(0.8) rotate(-1deg); 
 } 
 to { 
   opacity: 1; 
   transform: scale(1) rotate(0deg); 
 } 
}

@keyframes flipInX {
 from {
   opacity: 0;
   transform: perspective(400px) rotateX(-90deg);
 }
 to {
   opacity: 1;
   transform: perspective(400px) rotateX(0deg);
 }
}

@keyframes bounceIn {
 0% {
   opacity: 0;
   transform: scale(0.3);
 }
 50% {
   opacity: 1;
   transform: scale(1.05);
 }
 70% {
   transform: scale(0.95);
 }
 100% {
   opacity: 1;
   transform: scale(1);
 }
}

/* 统一动画类 */
.anim-in { 
 animation: slideInFromBottom 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) both; 
}

.anim-slide-left {
 animation: slideInFromLeft 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both; /* 更平滑的动画曲线和更快的速度 */
}

.anim-slide-right {
 animation: slideInFromRight 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) both;
}

.anim-zoom {
 animation: zoomIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) both;
}

.anim-flip {
 animation: flipInX 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) both;
}

.anim-bounce {
 animation: bounceIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) both;
}

/* 列表项动画 - 只对特定页面启用 */
#token-list li { 
 animation: slideInFromLeft 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) both; 
}

#token-list li:nth-child(odd) { 
 animation-name: slideInFromLeft;
 animation-delay: calc(var(--item-index, 0) * 0.08s);
}

#token-list li:nth-child(even) { 
 animation-name: slideInFromRight;
 animation-delay: calc(var(--item-index, 0) * 0.08s);
}

/* 弹幕库和任务页面的列表项不使用动画 */
#library-list li,
#tasks-list li { 
 animation: none;
}

.list li:nth-child(1) { --item-index: 0; }
.list li:nth-child(2) { --item-index: 1; }
.list li:nth-child(3) { --item-index: 2; }
.list li:nth-child(4) { --item-index: 3; }
.list li:nth-child(5) { --item-index: 4; }
.list li:nth-child(6) { --item-index: 5; }
.list li:nth-child(7) { --item-index: 6; }
.list li:nth-child(8) { --item-index: 7; }
.list li:nth-child(9) { --item-index: 8; }
.list li:nth-child(10) { --item-index: 9; }

/* 卡片进入动画 */
.card {
 animation: zoomIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) both;
}

/* 按钮动画 */
.library-btn, .row-action {
 animation: bounceIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) both;
}

/* 为不同按钮添加延迟 */
.library-actions .library-btn:nth-child(1) {
 animation-delay: 0.1s;
}

.library-actions .library-btn:nth-child(2) {
 animation-delay: 0.15s;
}

.library-actions .library-btn:nth-child(3) {
 animation-delay: 0.2s;
}


.library-actions .library-btn:nth-child(4) {
 animation-delay: 0.25s;
}


/* Results 结果列表 */
#results-card h2 { 
 margin: 6px 0 16px; 
 font-size: 18px; 
 font-weight: 700;
 color: var(--text);
}

.list { 
 list-style: none; 
 margin: 0; 
 padding: 0; 
}

.list li { 
 display: grid; 
 grid-template-columns: 48px 1fr auto; 
 gap: 16px; 
 align-items: center; 
 padding: 16px 0; 
 border-bottom: 1px solid var(--glass-border);
 transition: all 0.3s ease;
 /* 滚动优化 */
 contain: layout style paint; /* 限制重排范围 */
 transform: translateZ(0); /* 硬件加速 */
}

.list li > * { 
 min-width: 0; 
}

.list li:last-child { 
 border-bottom: none; 
}

.list li:hover {
 background: rgba(255, 255, 255, 0.05);
 border-radius: var(--radius-md);
 padding-left: 8px;
 padding-right: 8px;
 margin-left: -8px;
 margin-right: -8px;
}

/* 弹幕库专用样式 */
.library-item {
 display: block !important;
 grid-template-columns: none !important;
 padding: 16px 0 !important;
}

.library-item-top {
 display: grid;
 grid-template-columns: 48px 1fr;
 gap: 16px;
 align-items: center;
 margin-bottom: 12px;
}

.library-actions {
 display: grid;
 grid-template-columns: 1fr 1fr 1fr;
 gap: 8px;
 margin-top: 8px;
}

.library-btn {
 height: 36px !important;
 padding: 0 12px !important;
 border-radius: var(--radius-sm) !important;
 font-size: 13px !important;
 font-weight: 600 !important;
 border: 1px solid var(--accent) !important;
 background: linear-gradient(135deg, var(--accent), var(--accent-dark)) !important;
 color: #fff !important;
 transition: all 0.3s ease !important;
 text-align: center;
 white-space: nowrap;
 position: relative;
 overflow: hidden;
}

.library-btn::before {
 content: '';
 position: absolute;
 top: 0;
 left: -100%;
 width: 100%;
 height: 100%;
 background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
 transition: left 0.6s ease;
}

.library-btn:hover {
 transform: translateY(-1px) !important;
 box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3) !important;
}

.library-btn:hover::before {
 left: 100%;
}

.library-btn:active {
 transform: translateY(0) scale(0.98) !important;
}

.library-btn-danger {
 background: rgba(239, 68, 68, 0.1) !important;
 border-color: rgba(239, 68, 68, 0.3) !important;
 color: var(--error) !important;
}

.library-btn-danger:hover {
 background: rgba(239, 68, 68, 0.2) !important;
}

/* 弹幕库头部 */
.library-header {
 display: flex;
 justify-content: space-between;
 align-items: center;
 margin-bottom: 16px;
}

.library-header h2 {
 margin: 0;
 border-bottom: none;
 padding-bottom: 0;
}

/* 弹幕库筛选输入框 */
.library-filter {
 margin-bottom: 16px;
}

.library-filter input {
 width: 100%;
 padding: 12px 16px;
 border: 1px solid var(--glass-border);
 border-radius: var(--radius-md);
 background: rgba(255, 255, 255, 0.08);
 backdrop-filter: blur(8px);
 color: var(--text);
 font-size: 14px;
 transition: all 0.3s ease;
 box-sizing: border-box;
}

.library-filter input::placeholder {
 color: var(--text-muted);
}

.library-filter input:focus {
 outline: none;
 border-color: var(--accent);
 box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
}

/* 弹幕库卡片需要相对定位来支持下拉刷新 */
#library-card {
 position: relative;
 overflow: hidden;
}

/* 加载指示器 */
.loading-indicator {
 display: flex;
 align-items: center;
 justify-content: center;
 gap: 12px;
 padding: 20px;
 color: var(--text-muted);
 font-size: 14px;
}

.loading-spinner {
 width: 20px;
 height: 20px;
 border: 2px solid var(--glass-border);
 border-top-color: var(--accent);
 border-radius: 50%;
 animation: spin 1s linear infinite;
}

/* 下拉刷新功能已移除 */


.poster { 
 width: 48px; 
 height: 68px; 
 border-radius: var(--radius-md); 
 object-fit: cover; 
 background: var(--glass-bg);
 backdrop-filter: blur(8px);
 box-shadow: var(--glass-shadow);
 image-rendering: auto; 
}
.date-cell {
   line-height: 1.3;
   font-size: 13px;
   text-align: right;
}
.date-cell .time-part {
   font-size: 12px;
   color: var(--muted);
}

.info { overflow: hidden; }
.title { 
 font-weight: 700; 
 font-size: 17px; 
 white-space: nowrap; 
 overflow: hidden; 
 text-overflow: ellipsis;
 color: var(--text);
}

.meta { 
 color: var(--text-muted); 
 font-size: 12px; 
 white-space: nowrap; 
 overflow: hidden; 
 text-overflow: ellipsis; 
}
.small { 
 font-size: 12px; 
 color: var(--text-muted); 
}

.chip { 
 font-size: 13px; 
 padding: 8px 16px; 
 border-radius: 20px; 
 background: var(--glass-bg);
 backdrop-filter: blur(8px);
 color: var(--text); 
 border: 1px solid var(--glass-border); 
 box-shadow: var(--glass-shadow);
 cursor: pointer;
 transition: all 0.3s ease;
}

.chip:hover {
 transform: translateY(-1px);
 background: rgba(255, 255, 255, 0.15);
}

button { 
 height: 44px; 
 padding: 0 20px; 
 border-radius: var(--radius-md); 
 border: 1px solid var(--glass-border); 
 background: var(--glass-bg);
 backdrop-filter: var(--glass-backdrop);
 -webkit-backdrop-filter: var(--glass-backdrop);
 color: var(--text); 
 font-size: 14px;
 font-weight: 500;
 box-shadow: var(--glass-shadow);
 cursor: pointer;
 transition: all 0.3s ease;
}

button:hover {
 transform: translateY(-1px);
 background: rgba(255, 255, 255, 0.15);
 box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Recent Search Chips with Delete Button */
#recent-list {
   display: flex;
   flex-wrap: wrap;
   gap: 12px 8px; /* row-gap column-gap */
}

.chip-wrapper {
   position: relative;
   display: inline-flex; /* Let it size to its content */
}

.chip-wrapper .chip {
   /* Add padding on the right to make space for the delete button */
   padding-right: 28px;
}

.chip-delete {
   position: absolute;
   right: 4px;
   top: 50%;
   transform: translateY(-50%);
   width: 20px;
   height: 20px;
   border-radius: 50%;
   background-color: var(--error);
   color: white;
   border: none;
   display: flex;
   align-items: center;
   justify-content: center;
   font-size: 16px;
   line-height: 1;
   cursor: pointer;
   padding: 0;
   transition: background-color 0.2s, transform 0.1s, opacity 0.2s;
   /* Override generic button styles */
   box-shadow: none;
}

.chip-delete:hover {
   opacity: 0.8;
}

.chip-delete:active {
   opacity: 0.6;
   transform: translateY(-50%) scale(0.9);
}

button:active { 
 transform: translateY(1px); 
 transition: transform .15s ease; 
}

.row-action { 
 background: linear-gradient(135deg, var(--accent), var(--accent-dark));
 color: #fff; 
 border: 1px solid var(--glass-border); 
 box-shadow: var(--glass-shadow);
 font-weight: 600;
 position: relative;
 overflow: hidden;

}

.row-action::before {
 content: '';
 position: absolute;
 top: 0;
 left: -100%;
 width: 100%;
 height: 100%;
 background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
 transition: left 0.6s ease;
}

.row-action:hover { background: linear-gradient(135deg, var(--accent), var(--accent-dark)); }
.row-action:hover::before { left: 100%; }
.row-action:active { 
 transform: translateY(1px);
 background: linear-gradient(135deg, var(--accent), var(--accent-dark));
}
.row-action:disabled {
 background: linear-gradient(135deg, var(--accent), var(--accent-dark));
 opacity: 0.6;
 cursor: not-allowed;
}

@media (min-width: 680px) {
 .content { max-width: 720px; margin: 0 auto; }
}

/* iOS 安全区域适配 */
@supports (padding: max(0px)) {
 .topbar { padding-top: max(10px, env(safe-area-inset-top)); }
}

/* 底部导航 - 重新设计 */
.bottom-nav { 
 position: fixed; 
 left: 50%;
 transform: translateX(-50%);
 bottom: 20px; 
 display: flex;
 justify-content: center;
 align-items: center;
 gap: 4px; 
 padding: 8px; 
 background: var(--glass-bg);
 backdrop-filter: var(--glass-backdrop);
 -webkit-backdrop-filter: var(--glass-backdrop);
 border: 1px solid var(--glass-border);
 border-radius: 25px;
 box-shadow: var(--glass-shadow);
 min-height: 50px;
 max-width: 320px;
 width: calc(100vw - 40px);
}

.bottom-nav .nav-btn { 
 position: relative; 
 background: transparent; 
 border: none; 
 border-radius: 20px; 
 padding: 12px 16px; 
 color: var(--text-muted); 
 font-size: 14px; /* 增大字体从 11px 到 14px */
 font-weight: 600;
 cursor: pointer;
 transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
 white-space: nowrap;
 flex: 1;
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center; /* 确保垂直居中 */
 gap: 4px;
 text-align: center; /* 确保文字水平居中 */

}

/* 移除小圆点 */
.bottom-nav .nav-btn::before {
 display: none; /* 完全隐藏小圆点 */
}

.bottom-nav .nav-btn.active { 
 background: var(--glass-bg);
 color: var(--accent-light);
 font-weight: 700;
 transform: scale(1.0);
 box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移除激活状态的小圆点样式 */

.bottom-nav .nav-btn:active {
 transform: scale(0.95);
 transition: transform 0.1s ease;
}

/* 修正 Token 日志布局 */
#token-log-list li {
   grid-template-columns: 1fr auto; /* 左侧信息占满，右侧时间固定 */
   align-items: start; /* 顶部对齐 */
}
#token-log-list .info .meta {
   white-space: normal; /* 允许UA换行 */
   word-break: break-all;
   margin-top: 4px;
}

/* Token 列表重新设计 */
#token-list .token-list-item {
   display: block !important;
   grid-template-columns: none !important;
   padding: 16px 0 !important;
}

.token-info-section {
   display: grid;
   grid-template-columns: 1fr auto 1fr;
   gap: 12px;
   align-items: center;
   margin-bottom: 16px;
}

.token-info-section .status-cell {
   text-align: center;
}

.token-info-section .status-cell .status-icon {
   font-size: 1.2em;
}

.token-info-section .status-cell .status-icon.enabled {
   color: var(--success);
}

.token-info-section .status-cell .status-icon.disabled {
   color: var(--error);
}

.token-info-section .time-cell {
   display: flex;
   flex-direction: column;
   gap: 6px;
}

.time-row {
   display: flex;
   align-items: center;
   gap: 8px;
}

.time-label-split {
   display: flex;
   flex-direction: column;
   text-align: center;
   font-size: 12px;
   color: var(--text-muted);
   line-height: 1.2;
   flex-shrink: 0;
}

.time-value-split {
   display: flex;
   flex-direction: column;
   text-align: left;
   font-size: 12px;
   line-height: 1.2;
   white-space: nowrap;
}

.time-value-split span:last-child {
   color: var(--text-muted);

}

/* Token 按钮布局 - 两排设计 */
.token-actions-section {
   display: grid;
   grid-template-columns: 1fr 1fr;
   grid-template-rows: 1fr 1fr;
   gap: 8px;
}

.token-btn {
 height: 36px !important;
 padding: 0 12px !important;
 border-radius: var(--radius-sm) !important;
 font-size: 13px !important;
 font-weight: 600 !important;
 border: 1px solid var(--accent) !important;
 background: linear-gradient(135deg, var(--accent), var(--accent-dark)) !important;
 color: #fff !important;
 transition: all 0.3s ease !important;
 text-align: center;
 white-space: nowrap;
 position: relative;
 overflow: hidden;
}

.token-btn::before {
 content: '';
 position: absolute;
 top: 0;
 left: -100%;
 width: 100%;
 height: 100%;
 background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
 transition: left 0.6s ease;
}

.token-btn:hover {
   box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3) !important;
}

.token-btn:hover::before {
   left: 100%;
}

.token-btn:active {
 transform: translateY(1px) scale(0.98);
}

.token-btn-danger {
   background: rgba(239, 68, 68, 0.1) !important;
   border-color: rgba(239, 68, 68, 0.3) !important;
   color: var(--error) !important;
}

.token-btn-danger:hover {
   background: rgba(239, 68, 68, 0.2) !important;
}

/* 导航指示器已移除 - 使用新的按钮激活状态 */

/* sections, grids */
.section { margin-top: 8px; margin-bottom: 12px; }
.grid-1 { display: grid; gap: 8px; }
.grid-2 { display: grid; grid-template-columns: 1fr auto; gap: 8px; align-items: center; }
.grid-2-no-gap { display: grid; grid-template-columns: 1fr 1fr; gap: 8px; }
.grid-3 { display: grid; grid-template-columns: 1fr 1fr auto; gap: 8px; align-items: center; }

/* 额外的美化效果 */

/* 卡片标题美化 */
.card h2 {
 margin: 0 0 16px 0;
 font-size: 18px;
 font-weight: 700;
 color: var(--text);
 padding-bottom: 8px;
}

/* 消息提示美化 */
.message {
 padding: 12px 16px;
 border-radius: 24px;
 font-size: 14px;
 font-weight: 500;
 backdrop-filter: blur(8px);
 border: 1px solid;
 margin: 8px 0;
}
/* 空消息默认隐藏，避免出现空白提示框 */
.message:empty { display: none; }

.message.success {
 background: rgba(16, 185, 129, 0.1);
 border-color: var(--success);
 color: var(--success);
}

.message.warning {
 background: rgba(245, 158, 11, 0.1);
 border-color: var(--warning);
 color: var(--warning);
}

/* 状态图标美化 */
.status-icon {
 font-size: 16px;
 filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 删除按钮优化 */
.chip-delete {
 background: linear-gradient(135deg, var(--error), #dc2626);
 box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* 响应式优化 */
@media (max-width: 480px) {
 .content {
   padding: 12px;
   gap: 12px;
   padding-bottom: 90px;
 }
 
 .card {
   padding: 16px;
 }
 
 .search-bar {
   grid-template-columns: 1fr;
   gap: 8px;
 }
 
 .search-bar .primary {
   width: 100%;
 }
 
 .subnav {
   grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
 }
 
 .bottom-nav {
   bottom: 10px;
   width: calc(100vw - 20px);
   max-width: none;
 }
 
 .bottom-nav .nav-btn {
   padding: 10px 8px;
   font-size: 12px; /* 小屏幕上也保持较大字体 */
 }
 
 .user {
   gap: 8px;
 }
 
 .topbar {
   padding: 12px 16px;
 }
 
 .brand-mini span {
   font-size: 16px;
 }
}

/* 深度毛玻璃效果增强 */
@supports (backdrop-filter: blur(20px)) {
 .card, .auth-card, .topbar, .bottom-nav {
   backdrop-filter: blur(20px) saturate(180%);
   -webkit-backdrop-filter: blur(20px) saturate(180%);
 }
}

/* 滚动优化 */
.content {
 scroll-behavior: smooth;
 /* 优化滚动性能 */
 overflow-y: auto;
 -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
 will-change: scroll-position; /* 提示浏览器优化滚动 */
}

/* 防止内容加载时跳动 */
.list, #library-list, #tasks-list, #token-list {
 min-height: 60px;
 /* 移除 min-height 过渡，避免数据切换时的轻微跳动 */
 /* transition: min-height 0.3s ease; */
 /* 优化滚动性能 */
 transform: translateZ(0); /* 硬件加速 */
 backface-visibility: hidden; /* 减少重绘 */
 will-change: transform; /* 提示浏览器优化变换 */
}

/* 加载状态时保持最小高度 */
.list:empty::before {
 content: '';
 display: block;
 height: 60px;
}

/* 确保内容平滑显示 */
.card {
 contain: layout style;
 /* 添加滚动容器优化 */
 overflow: hidden; /* 防止内容溢出影响滚动 */
}

/* 弹幕库特殊优化 */
#library-card {
 /* 启用硬件加速 */
 transform: translateZ(0);
 backface-visibility: hidden;
}

/* 骨架屏样式 */
.skeleton-container {
 padding: 16px 0;
 animation: fadeIn 0.3s ease-out;
}

.skeleton-item {
 display: grid;
 grid-template-columns: 48px 1fr;
 gap: 16px;
 align-items: center;
 margin-bottom: 16px;
 padding: 16px 0;
 border-bottom: 1px solid var(--glass-border);
}

.skeleton-item:last-child {
 border-bottom: none;
}

.skeleton-avatar {
 width: 48px;
 height: 48px;
 border-radius: var(--radius-md);
 background: linear-gradient(90deg, 
   var(--glass-bg) 25%, 
   var(--glass-border) 50%, 
   var(--glass-bg) 75%
 );
 background-size: 200% 100%;
 animation: skeleton-shimmer 2s infinite;
}

.skeleton-content {
 display: flex;
 flex-direction: column;
 gap: 8px;
}

.skeleton-title {
 height: 16px;
 width: 80%;
 border-radius: var(--radius-sm);
 background: linear-gradient(90deg, 
   var(--glass-bg) 25%, 
   var(--glass-border) 50%, 
   var(--glass-bg) 75%
 );
 background-size: 200% 100%;
 animation: skeleton-shimmer 2s infinite;
}

.skeleton-subtitle {
 height: 12px;
 width: 60%;
 border-radius: var(--radius-sm);
 background: linear-gradient(90deg, 
   var(--glass-bg) 25%, 
   var(--glass-border) 50%, 
   var(--glass-bg) 75%
 );
 background-size: 200% 100%;
 animation: skeleton-shimmer 2s infinite;
 animation-delay: 0.2s;
}

.skeleton-actions {
 display: flex;
 justify-content: flex-end;
}

.skeleton-button {
 height: 32px;
 width: 60px;
 border-radius: var(--radius-sm);
 background: linear-gradient(90deg, 
   var(--glass-bg) 25%, 
   var(--glass-border) 50%, 
   var(--glass-bg) 75%
 );
 background-size: 200% 100%;
 animation: skeleton-shimmer 2s infinite;
 animation-delay: 0.4s;
}

/* 弹幕库特殊骨架屏样式 */
.library-skeleton-item {
 border-bottom: none !important;
 margin-bottom: 8px !important;
 padding-bottom: 8px !important;
}

.skeleton-actions-row {
 display: grid;
 grid-template-columns: 1fr 1fr 1fr;
 gap: 8px;
 margin-bottom: 24px;
}

.skeleton-actions-row .skeleton-button {
 width: 100%;
 height: 36px;
}

/* 骨架屏闪烁动画 */
@keyframes skeleton-shimmer {
 0% {
   background-position: -200% 0;
 }
 100% {
   background-position: 200% 0;
 }
}

/* 默认隐藏骨架屏 */
.skeleton-container {
 display: none;
}

/* 显示骨架屏 */
.skeleton-container.show {
 display: block;
 animation: fadeIn 0.3s ease-out;
}

/* 为不同的骨架屏元素添加交错动画延迟 */
.skeleton-item:nth-child(1) .skeleton-avatar,
.skeleton-item:nth-child(1) .skeleton-title,
.skeleton-item:nth-child(1) .skeleton-subtitle {
 animation-delay: 0s;
}

.skeleton-item:nth-child(2) .skeleton-avatar,
.skeleton-item:nth-child(2) .skeleton-title,
.skeleton-item:nth-child(2) .skeleton-subtitle {
 animation-delay: 0.1s;
}

.skeleton-item:nth-child(3) .skeleton-avatar,
.skeleton-item:nth-child(3) .skeleton-title,
.skeleton-item:nth-child(3) .skeleton-subtitle {
 animation-delay: 0.2s;
}

.skeleton-actions-row:nth-child(2) .skeleton-button {
 animation-delay: 0.1s;
}

.skeleton-actions-row:nth-child(4) .skeleton-button {
 animation-delay: 0.2s;
}

.skeleton-actions-row:nth-child(6) .skeleton-button {
 animation-delay: 0.3s;
}

/* 触摸反馈优化 */
@media (hover: none) and (pointer: coarse) {
 /* 移动端禁用 fixed 背景附着与固定背景层，避免内容更新时的整体抖动 */
 body { background-attachment: scroll; }
 body::before { position: absolute; }
 /* 禁用容器入场动画，避免切换时整体闪烁 */
 .content { animation: none !important; }

 /* 移动端禁用库、任务、Token 相关卡片的入场动画，避免数据渲染瞬间的抖动 */
 #library-card, #tasks-card, #tokens-card, #tokens-ua-card, #tokens-log-card { animation: none !important; }
 /* 顶部与底部导航在触摸设备上也不做入场动画，避免重绘抖动 */
 .topbar, .bottom-nav { animation: none !important; }

 /* 仅保留骨架屏动画：禁用库与任务中按钮/行项的入场动画 */
 #library-card .library-btn,
 #library-card .row-action,
 #library-list li,
 #tasks-card .row-action,
 #tasks-list li,
 #token-list li { animation: none !important; }

 /* Token 相关卡片启用硬件加速，减少重绘导致的闪烁 */
 #tokens-card, #tokens-ua-card, #tokens-log-card {
   transform: translateZ(0);
   backface-visibility: hidden;
   will-change: transform;
 }

 /* 移动端禁用列表项 hover 时的位移与内边距改变，避免首次渲染或触摸触发时的抖动 */
 .list li:hover {
   background: none;
   padding-left: 0;
   padding-right: 0;
   margin-left: 0;
   margin-right: 0;
 }

 /* 移动端不对列表容器的最小高度做动画，以免高度变化引发跳动 */
 .list, #library-list, #tasks-list, #token-list { transition: none; }

 button:hover {
   transform: none;
   background: inherit; /* 触摸设备取消全局 hover 背景，避免按钮点击后发白 */
 }
 
 .card:hover {
   transform: none;
 }
 
 .list li:hover {
   background: none;
 }
 
 button:active {
   transform: scale(0.98);
 }
 
 .card:active {
   transform: scale(0.995);
 }
 
 .bottom-nav .nav-btn:active {
   transform: scale(0.9) !important;
 }
}

/* 任务进度条样式 */
.task-progress-container {
 display: flex;
 align-items: center;
 justify-content: center;
 margin: 20px 0;
 position: relative;
}

.task-progress-ring {
 width: 120px;
 height: 120px;
 position: relative;
 display: flex;
 align-items: center;
 justify-content: center;
}

.task-progress-ring svg {
 width: 100%;
 height: 100%;
 transform: rotate(-90deg);
 position: absolute;
 top: 0;
 left: 0;
}

.task-progress-ring .track {
 fill: none;
 stroke: var(--glass-border);
 stroke-width: 8;
 opacity: 0.3;
}

.task-progress-ring .progress {
 fill: none;
 stroke-width: 8;
 stroke-linecap: round;
 transition: stroke-dashoffset 0.6s cubic-bezier(0.4, 0, 0.2, 1);
 filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.4));
}

/* 任务进度渐变 */
.task-progress-ring .gradient-stop-1 {
 stop-color: #6366f1;
}

.task-progress-ring .gradient-stop-2 {
 stop-color: #06b6d4;
}

.task-progress-ring .gradient-stop-3 {
 stop-color: #10b981;
}

/* 任务进度动画 - 彩虹效果 */
@keyframes task-progress-rainbow {
 0% {
   stroke: #ff6b6b;
   filter: drop-shadow(0 0 12px rgba(255, 107, 107, 0.6));
 }
 16.66% {
   stroke: #ff8c00;
   filter: drop-shadow(0 0 12px rgba(255, 140, 0, 0.6));
 }
 33.33% {
   stroke: #ffd700;
   filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.6));
 }
 50% {
   stroke: #9370db;
   filter: drop-shadow(0 0 12px rgba(147, 112, 219, 0.6));
 }
 66.66% {
   stroke: #4169e1;
   filter: drop-shadow(0 0 12px rgba(65, 105, 225, 0.6));
 }
 83.33% {
   stroke: #00ced1;
   filter: drop-shadow(0 0 12px rgba(0, 206, 209, 0.6));
 }
 100% {
   stroke: #ff6b6b;
   filter: drop-shadow(0 0 12px rgba(255, 107, 107, 0.6));
 }
}

.task-progress-ring .progress.animated {
 animation: task-progress-rainbow 3s linear infinite;
}

.task-progress-ring .progress.completed {
 stroke: #10b981;
 filter: drop-shadow(0 0 16px rgba(16, 185, 129, 0.8));
 animation: pulse-green 1s ease-in-out;
}

@keyframes pulse-green {
 0%, 100% {
   filter: drop-shadow(0 0 16px rgba(16, 185, 129, 0.8));
 }
 50% {
   filter: drop-shadow(0 0 24px rgba(16, 185, 129, 1));
 }
}

/* 任务进度中心内容 */
.task-progress-center {
 position: absolute;
 top: 50%;
 left: 50%;
 transform: translate(-50%, -50%);
 text-align: center;
 z-index: 2;
}

.task-progress-percentage {
 font-size: 24px;
 font-weight: 700;
 color: var(--text);
 text-shadow: var(--text-shadow);
 margin-bottom: 4px;
 display: block;
}

.task-progress-label {
 font-size: 12px;
 color: var(--text-muted);
 font-weight: 500;
 text-transform: uppercase;
 letter-spacing: 0.5px;
}

/* 任务统计信息 */
.task-stats {
 display: grid;
 grid-template-columns: repeat(4, 1fr);
 gap: 8px;
 margin: 16px 0;
 padding: 16px;
 background: var(--glass-bg);
 backdrop-filter: var(--glass-backdrop);
 -webkit-backdrop-filter: var(--glass-backdrop);
 border: 1px solid var(--glass-border);
 border-radius: var(--radius-md);
 box-shadow: var(--glass-shadow);
}

.task-stat-item {
 text-align: center;
 padding: 8px;
}

.task-stat-number {
 font-size: 20px;
 font-weight: 700;
 color: var(--text);
 margin-bottom: 4px;
 display: block;
}

.task-stat-label {
 font-size: 11px;
 color: var(--text-muted);
 font-weight: 500;
 text-transform: uppercase;
 letter-spacing: 0.3px;
}

/* 状态颜色 */
.task-stat-item.running .task-stat-number {
 color: var(--primary);
}

.task-stat-item.completed .task-stat-number {
 color: var(--success);
}

.task-stat-item.failed .task-stat-number {
 color: var(--error);
}

.task-stat-item.queued .task-stat-number {
 color: var(--warning);
}

/* 任务列表进度条样式增强 */
.task-progress-bar-container {
 position: relative;
 height: 8px;
 background: var(--glass-border);
 border-radius: 4px;
 overflow: hidden;
 margin-top: 8px;
 backdrop-filter: blur(4px);
}

.task-progress-bar {
 height: 100%;
 border-radius: 4px;
 transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
 position: relative;
 overflow: hidden;
}

/* 进度条发光效果 */
.task-progress-bar::before {
 content: '';
 position: absolute;
 top: 0;
 left: -100%;
 width: 100%;
 height: 100%;
 background: linear-gradient(90deg, 
   transparent, 
   rgba(255, 255, 255, 0.4), 
   transparent
 );
 animation: progress-shine 2s ease-in-out infinite;
}

/* 已完成和失败的任务不显示光效 */
.task-item[data-status="已完成"] .task-progress-bar::before,
.task-item[data-status="失败"] .task-progress-bar::before {
 display: none;
}

@keyframes progress-shine {
 0% {
   left: -100%;
 }
 50% {
   left: 100%;
 }
 100% {
   left: 100%;
 }
}

/* 任务项选中状态美化 */
.task-item.selected {
 background: var(--glass-bg);
 border: 2px solid var(--accent);
 border-radius: var(--radius-md);
 box-shadow: 
   var(--glass-shadow),
   0 0 0 3px rgba(6, 182, 212, 0.2);
 transform: translateY(-2px);
}

.task-item.selected .task-progress-bar {
 box-shadow: 0 0 8px rgba(6, 182, 212, 0.4);
}

/* 深色模式适配 */
[data-theme="dark"] .task-progress-ring .track {
 stroke: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .task-stats {
 background: rgba(17, 24, 39, 0.82);
 border-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .task-progress-bar-container {
 background: rgba(255, 255, 255, 0.1);
}

/* 响应式优化 */
@media (max-width: 480px) {
 .task-progress-ring {
   width: 100px;
   height: 100px;
 }
 
 .task-progress-percentage {
   font-size: 20px;
 }
 
 .task-stats {
   gap: 6px;
   padding: 12px 8px;
   grid-template-columns: repeat(4, 1fr);
 }
 
 .task-stat-item {
   padding: 6px 2px;
 }
 
 .task-stat-number {
   font-size: 16px;
 }
 
 .task-stat-label {
   font-size: 10px;
 }
}

/* 页面加载动画 */
@keyframes pageLoad {
 from {
   opacity: 0;
   transform: translateY(30px) scale(0.95);
 }
 to {
   opacity: 1;
   transform: translateY(0) scale(1);
 }
}

@keyframes staggerIn {
 from {
   opacity: 0;
   transform: translateY(20px) rotateX(-10deg);
 }
 to {
   opacity: 1;
   transform: translateY(0) rotateX(0deg);
 }
}

.content {
 animation: pageLoad 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) both;
}

/* 特殊元素的入场动画 */
.topbar {
 animation: slideInFromBottom 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) both;
 animation-delay: 0.1s;
}

.bottom-nav {
 animation: slideUpFade 0.5s ease-out 0.5s both;
}

/* 搜索栏动画 */
.search-bar {
 animation: staggerIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) both;
 animation-delay: 0.2s;
}

/* 主题切换动画 */
html {
 transition: color-scheme 0.3s ease;
}

[data-theme] {
 transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* 导航栏进入动画 */
.bottom-nav {
 animation: slideUpFade 0.5s ease-out 0.3s both;
}

@keyframes slideUpFade {
 from {
   opacity: 0;
   transform: translate(-50%, 100%);
 }
 to {
   opacity: 1;
   transform: translate(-50%, 0);
 }

}
