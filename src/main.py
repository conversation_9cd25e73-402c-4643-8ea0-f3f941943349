import uvicorn
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Depends
import logging
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, HTMLResponse, RedirectResponse
import json
from .config_manager import ConfigManager
from .database import create_db_pool, close_db_pool, init_db_tables, create_initial_admin_user
from .api.ui import router as ui_router, auth_router
from .api.bangumi_api import router as bangumi_router
from .api.tmdb_api import router as tmdb_router
from .api.webhook_api import router as webhook_router
from .api.imdb_api import router as imdb_router
from .api.tvdb_api import router as tvdb_router
from .api.douban_api import router as douban_router
from .dandan_api import dandan_router
from .task_manager import TaskManager
from .metadata_manager import MetadataSourceManager
from .scraper_manager import ScraperManager
from .webhook_manager import WebhookManager
from .scheduler import SchedulerManager
from .config import settings
from . import crud, security
from .log_manager import setup_logging

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理器。
    - `yield` 之前的部分在应用启动时执行。 
    - `yield` 之后的部分在应用关闭时执行。
    """
    # --- Startup Logic ---
    setup_logging()


    pool = await create_db_pool(app)
    await init_db_tables(app)
    # 新增：在启动时清理任何未完成的任务
    interrupted_count = await crud.mark_interrupted_tasks_as_failed(pool)
    if interrupted_count > 0:
        logging.getLogger(__name__).info(f"已将 {interrupted_count} 个中断的任务标记为失败。")
    
    # 新增：初始化配置管理器
    app.state.config_manager = ConfigManager(pool)
    # 新增：集中定义所有默认配置
    default_configs = {
        # 缓存 TTL
        'search_ttl_seconds': (10800, '搜索结果的缓存时间（秒），最低3小时。'),
        'episodes_ttl_seconds': (10800, '分集列表的缓存时间（秒），最低3小时。'),
        'base_info_ttl_seconds': (10800, '基础媒体信息（如爱奇艺）的缓存时间（秒），最低3小时。'),
        'metadata_search_ttl_seconds': (10800, '元数据（如TMDB, Bangumi）搜索结果的缓存时间（秒），最低3小时。'),
        # API 和 Webhook
        'custom_api_domain': ('', '用于拼接弹幕API地址的自定义域名。'),
        'webhook_api_key': ('', '用于Webhook调用的安全密钥。'),
        'webhook_custom_domain': ('', '用于拼接Webhook URL的自定义域名。'),
        # 认证
        # 代理
        'proxy_url': ('', '全局HTTP/HTTPS/SOCKS5代理地址。'),
        'proxy_enabled': ('false', '是否全局启用代理。'),
        'jwt_expire_minutes': (settings.jwt.access_token_expire_minutes, 'JWT令牌的有效期（分钟）。-1 表示永不过期。'),
        # 元数据源
        'tmdb_api_key': ('', '用于访问 The Movie Database API 的密钥。'),
        'tmdb_api_base_url': ('https://api.themoviedb.org', 'TMDB API 的基础域名。'),
        'tmdb_image_base_url': ('https://image.tmdb.org', 'TMDB 图片服务的基础 URL。'),
        'tvdb_api_key': ('', '用于访问 TheTVDB API 的密钥。'),
        'bangumi_client_id': ('', '用于Bangumi OAuth的App ID。'),
        'bangumi_client_secret': ('', '用于Bangumi OAuth的App Secret。'),
        'douban_cookie': ('', '用于访问豆瓣API的Cookie。'),
        # 弹幕源
        'bilibili_cookie': ('', '用于访问B站API的Cookie，特别是buvid3。'),
        'gamer_cookie': ('', '用于访问巴哈姆特动画疯的Cookie。'),
        'gamer_user_agent': ('', '用于访问巴哈姆特动画疯的User-Agent。'),
    }
    await app.state.config_manager.register_defaults(default_configs)

    app.state.scraper_manager = ScraperManager(pool, app.state.config_manager)
    await app.state.scraper_manager.load_and_sync_scrapers()
    # 新增：初始化元数据源管理器
    app.state.metadata_manager = MetadataSourceManager(pool)
    await app.state.metadata_manager.initialize()

    app.state.task_manager = TaskManager(pool)
    # 修正：将 ConfigManager 传递给 WebhookManager
    app.state.webhook_manager = WebhookManager(
        pool, app.state.task_manager, app.state.scraper_manager, app.state.config_manager
    )
    app.state.task_manager.start()
    await create_initial_admin_user(app)
    app.state.cleanup_task = asyncio.create_task(cleanup_task(app))
    app.state.scheduler_manager = SchedulerManager(pool, app.state.task_manager, app.state.scraper_manager)
    await app.state.scheduler_manager.start()
    
    yield
    
    # --- Shutdown Logic ---
    if hasattr(app.state, "cleanup_task"):
        app.state.cleanup_task.cancel()
        try:
            await app.state.cleanup_task
        except asyncio.CancelledError:
            pass
    await close_db_pool(app)
    if hasattr(app.state, "scraper_manager"):
        await app.state.scraper_manager.close_all()
    if hasattr(app.state, "task_manager"):
        await app.state.task_manager.stop()
    if hasattr(app.state, "scheduler_manager"):
        await app.state.scheduler_manager.stop()


app = FastAPI(title="Danmaku API", description="一个基于dandanplay API风格的弹幕服务", version="1.0.0", lifespan=lifespan)

@app.middleware("http")
async def log_not_found_requests(request: Request, call_next):
    """
    中间件：捕获所有请求，如果响应是 404 Not Found，
    则以JSON格式记录详细的请求入参，方便调试。
    """
    response = await call_next(request)
    if response.status_code == 404:
        # 创建一个可序列化的 ASGI scope 副本以进行详细日志记录
        scope = request.scope
        serializable_scope = {
            "type": scope.get("type"),
            "http_version": scope.get("http_version"),
            "server": scope.get("server"),
            "client": scope.get("client"),
            "scheme": scope.get("scheme"),
            "method": scope.get("method"),
            "root_path": scope.get("root_path"),
            "path": scope.get("path"),
            "raw_path": scope.get("raw_path", b"").decode("utf-8", "ignore"),
            "query_string": scope.get("query_string", b"").decode("utf-8", "ignore"),
            "headers": {h[0].decode("utf-8", "ignore"): h[1].decode("utf-8", "ignore") for h in scope.get("headers", [])},
        }
        log_details = {
            "message": "HTTP 404 Not Found - 未找到匹配的API路由",
            "url": str(request.url),
            "raw_request_scope": serializable_scope
        }
        logging.getLogger(__name__).warning("未处理的请求详情 (原始请求范围):\n%s", json.dumps(log_details, indent=2, ensure_ascii=False))
    return response

async def cleanup_task(app: FastAPI):
    """定期清理过期缓存和OAuth states的后台任务。"""
    pool = app.state.db_pool
    while True:
        try:
            await asyncio.sleep(3600) # 每小时清理一次
            await crud.clear_expired_cache(pool)
            await crud.clear_expired_oauth_states(app.state.db_pool)
        except asyncio.CancelledError:
            break
        except Exception as e:
            logging.getLogger(__name__).error(f"缓存清理任务出错: {e}")

# 挂载静态文件目录
# 注意：这应该在项目根目录运行，以便能找到 'static' 文件夹
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/images", StaticFiles(directory="config/image"), name="images")

# 包含 v2 版本的 API 路由
app.include_router(ui_router, prefix="/api/ui", tags=["Web UI API"])
app.include_router(auth_router, prefix="/api/ui/auth", tags=["Auth"])
app.include_router(bangumi_router, prefix="/api/bgm", tags=["Bangumi"])
app.include_router(tmdb_router, prefix="/api/tmdb", tags=["TMDB"])
app.include_router(douban_router, prefix="/api/douban", tags=["Douban"])
app.include_router(imdb_router, prefix="/api/imdb", tags=["IMDb"])
app.include_router(tvdb_router, prefix="/api/tvdb", tags=["TVDB"])
app.include_router(webhook_router, prefix="/api/webhook", tags=["Webhook"])

# 将最通用的 dandan_router 挂载在最后，以避免路径冲突。
# 这样可以确保像 /api/ui 这样的静态路径会优先于 /api/{token} 被匹配。
app.include_router(dandan_router, prefix="/api", tags=["DanDanPlay Compatible"])

# 根路径返回前端页面
@app.get("/", include_in_schema=False)
async def read_index(request: Request):
    # 支持通过查询参数关闭移动端跳转：/?desktop=1
    if request.query_params.get("desktop") == "1":
        return FileResponse("static/index.html")

    ua = request.headers.get("user-agent", "").lower()
    is_mobile = any(
        kw in ua for kw in [
            "iphone",
            "android",
            "ipad",
            "windows phone",
            "mobile",
            "opera mini",
            "mobile safari",
        ]
    )
    if is_mobile:
        return RedirectResponse(url="/m", status_code=307)
    return FileResponse("static/index.html")

# 移动端页面
@app.get("/m", response_class=FileResponse, include_in_schema=False)
async def read_mobile():
    return "static/mobile.html"

# 添加一个运行入口，以便直接从配置启动
# 这样就可以通过 `python -m src.main` 来运行，并自动使用 config.yml 中的端口和主机
if __name__ == "__main__":
    uvicorn.run(
        app,
        host=settings.server.host,
        port=settings.server.port
    )