version: '3.8'

services:
  app:
    # 使用本地 Dockerfile 构建镜像。
    build:
      context: . # Dockerfile 将创建一个默认用户，权限由 entrypoint.sh 在运行时处理。
    # image: l429609201/danmu_api_server:latest # 如果您想使用自己推送的镜像，请取消注释此行并注释掉上面的 'build: .'
    container_name: danmu-api
    restart: unless-stopped
    # 使用主机网络模式，容器将直接使用宿主机的网络。
    # 这意味着容器内的 127.0.0.1 就是宿主机的 127.0.0.1。
    # 应用将直接在宿主机的 7768 端口上可用，无需端口映射。
    network_mode: "host"
    environment:
      # Pydantic-settings 会自动读取这些环境变量
      # 将 PUID/PGID 作为环境变量传递给容器，供 entrypoint.sh 使用
      - PUID=1000
      - PGID=1000
      - UMASK=0022 # 设置文件创建掩码，0022 是一个常见的安全默认值
      # 服务配置 (端口在主机网络模式下由应用内部配置决定)
      - DANMUAPI_SERVER__HOST=0.0.0.0
      - DANMUAPI_SERVER__PORT=7768
      # 数据库配置 - 连接到宿主机上的数据库
      - DANMUAPI_DATABASE__HOST=127.0.0.1
      - DANMUAPI_DATABASE__PORT=3306
      - DANMUAPI_DATABASE__USER=danmuapi
      - DANMUAPI_DATABASE__PASSWORD=danmuapi
      - DANMUAPI_DATABASE__NAME=danmuapi
      # 新增：日志级别配置 (可选值: DEBUG, INFO, WARNING, ERROR)
      # - DANMUAPI_LOG__LEVEL=DEBUG
      # JWT 配置 - 强烈建议在 .env 文件中设置一个强密钥
      #- DANMUAPI_JWT__SECRET_KEY=${JWT_SECRET_KEY:-a_very_secret_key_that_should_be_changed_in_prod}
      # 初始管理员配置
      - DANMUAPI_ADMIN__INITIAL_USER=admin
    volumes:
      # 挂载 config 目录以持久化日志和配置文件。
      - ./config:/app/config

# 由于不再使用容器化的数据库，db 服务、volumes 和 networks 部分已被移除。
# 请确保您的宿主机上已经运行了 MySQL 服务，并且：
# 1. 监听在 127.0.0.1:3306。
# 2. 创建了名为 'danmuapi' 的数据库。
# 3. 创建了用户 'danmuapi'，密码为 'danmuapi'，并授予了对 'danmuapi' 数据库的访问权限。
