# 弹幕聚合服务

将多个弹幕接口的数据聚合并转换为dandanplay兼容格式的独立服务。

## 功能特性

- 🔄 **多源聚合**: 同时从5个弹幕源获取数据
- 🎯 **格式转换**: 自动识别JSON/XML格式并转换为dandanplay格式
- 🚀 **并发请求**: 异步并发请求所有弹幕源，提高响应速度
- 🔍 **智能去重**: 基于时间和内容的弹幕去重算法
- 📊 **标准兼容**: 完全兼容dandanplay API格式

## 支持的弹幕源

1. `https://api.danmu.icu/?ac=dm&url=` (JSON格式)
2. `https://dmku.hls.one/?ac=dm&url=` (JSON格式)
3. `http://dm.apptotv.top/?ac=dm&url=` (JSON格式)
4. `https://danmu.56uxi.com/?ac=dm&url=` (JSON格式)
5. `https://fc.lyz05.cn/?url=` (XML格式)

## 快速开始

### 安装依赖

```bash
cd danmu_aggregator
pip install -r requirements.txt
```

### 启动服务

```bash
python main.py
```

服务将在 `http://localhost:8080` 启动。

### API使用

#### 获取弹幕

```http
GET /danmu?url=https://example.com/video.m3u8&chConvert=1
```

**参数说明:**
- `url`: 视频播放地址 (必需)
- `chConvert`: 简繁转换 (可选)
  - `0`: 不转换 (默认)
  - `1`: 转换为简体中文
  - `2`: 转换为繁体中文

**响应格式:**
```json
{
  "count": 1250,
  "comments": [
    {
      "cid": 1,
      "p": "15.5,1,16777215,1",
      "m": "这里好燃啊！"
    },
    {
      "cid": 2,
      "p": "32.1,4,65280,2", 
      "m": "画面太美了"
    }
  ]
}
```

**弹幕参数说明 (p字段):**
- 格式: `时间,模式,颜色,来源`
- 时间: 弹幕出现时间(秒)
- 模式: 1=滚动, 4=底部, 5=顶部
- 颜色: RGB颜色值
- 来源: 弹幕源标识

#### 服务状态

```http
GET /
```

#### 健康检查

```http
GET /health
```

## 数据格式支持

### JSON格式 (前4个接口)

```json
{
  "code": 23,
  "name": "hash",
  "danum": 3,
  "danmuku": [
    [时间, "位置", "#颜色", "", "内容", "", "", "字体大小"]
  ]
}
```

### XML格式 (最后1个接口)

```xml
<?xml version="1.0" encoding="utf-8"?>
<i>
<d p="时间,模式,字体大小,颜色,时间戳,池,用户ID,弹幕ID,权重">弹幕内容</d>
</i>
```

## 集成到主项目

可以将此服务作为你主项目的一个弹幕源，在主项目的scraper中添加对此服务的调用:

```python
# 在主项目中调用聚合服务
async def get_aggregated_danmu(video_url: str):
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"http://localhost:8080/danmu?url={quote(video_url)}&chConvert=1"
        )
        return response.json()
```

## 部署建议

### Docker部署

创建 `Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY main.py .
EXPOSE 8080

CMD ["python", "main.py"]
```

### 生产环境

```bash
# 使用gunicorn部署
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8080
```

## 注意事项

1. **网络超时**: 每个弹幕源请求超时设置为10秒
2. **错误处理**: 单个源失败不影响其他源的数据获取
3. **去重算法**: 基于时间(精确到0.01秒)+内容进行去重
4. **并发限制**: 建议根据服务器性能调整并发数量
5. **缓存优化**: 生产环境建议添加Redis缓存层

## 扩展功能

- [ ] 简繁转换实现
- [ ] Redis缓存支持
- [ ] 弹幕过滤功能
- [ ] 更多弹幕源支持
- [ ] 监控和统计功能
