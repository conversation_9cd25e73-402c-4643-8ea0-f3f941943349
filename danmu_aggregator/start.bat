@echo off
chcp 65001 >nul

echo 🚀 弹幕聚合服务启动脚本
echo ==========================

REM 检查Python版本
echo 📋 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查是否在正确的目录
if not exist "main.py" (
    echo ❌ 错误: 请在danmu_aggregator目录中运行此脚本
    pause
    exit /b 1
)

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo 🔧 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 🔌 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 📦 安装依赖...
pip install -r requirements.txt

REM 启动服务
echo 🚀 启动弹幕聚合服务...
echo 服务地址: http://localhost:8080
echo API文档: http://localhost:8080/docs
echo 按 Ctrl+C 停止服务
echo.

python main.py

pause
