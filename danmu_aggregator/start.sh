#!/bin/bash

# 弹幕聚合服务启动脚本

set -e

echo "🚀 弹幕聚合服务启动脚本"
echo "=========================="

# 检查Python版本
echo "📋 检查Python环境..."
python3 --version

# 检查是否在正确的目录
if [ ! -f "main.py" ]; then
    echo "❌ 错误: 请在danmu_aggregator目录中运行此脚本"
    exit 1
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "🔧 创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔌 激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📦 安装依赖..."
pip install -r requirements.txt

# 启动服务
echo "🚀 启动弹幕聚合服务..."
echo "服务地址: http://localhost:8080"
echo "API文档: http://localhost:8080/docs"
echo "按 Ctrl+C 停止服务"
echo ""

python main.py
