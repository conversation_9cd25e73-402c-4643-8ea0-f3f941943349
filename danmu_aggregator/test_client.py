#!/usr/bin/env python3
"""
弹幕聚合服务测试客户端
"""

import asyncio
import json
from urllib.parse import quote

import httpx

async def test_danmu_service():
    """测试弹幕聚合服务"""
    
    # 测试视频URL (可以替换为实际的视频地址)
    test_urls = [
        "https://example.com/video1.m3u8",
        "https://www.bilibili.com/video/BV1xx411c7mu",
        "https://v.qq.com/x/cover/example.html"
    ]
    
    base_url = "http://localhost:8080"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("🚀 开始测试弹幕聚合服务...")
        
        # 1. 测试服务状态
        print("\n📊 检查服务状态...")
        try:
            response = await client.get(f"{base_url}/")
            print(f"✅ 服务状态: {response.json()}")
        except Exception as e:
            print(f"❌ 服务状态检查失败: {e}")
            return
        
        # 2. 测试健康检查
        print("\n🏥 健康检查...")
        try:
            response = await client.get(f"{base_url}/health")
            print(f"✅ 健康状态: {response.json()}")
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
        
        # 3. 测试弹幕获取
        for i, test_url in enumerate(test_urls, 1):
            print(f"\n🎬 测试视频 {i}: {test_url}")
            
            try:
                # 构建请求URL
                encoded_url = quote(test_url, safe='')
                request_url = f"{base_url}/danmu?url={encoded_url}&chConvert=1"
                
                print(f"📡 请求URL: {request_url}")
                
                # 发送请求
                response = await client.get(request_url)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 成功获取弹幕:")
                    print(f"   - 弹幕总数: {data.get('count', 0)}")
                    
                    # 显示前几条弹幕示例
                    comments = data.get('comments', [])
                    if comments:
                        print(f"   - 弹幕示例:")
                        for j, comment in enumerate(comments[:3]):
                            p_parts = comment['p'].split(',')
                            time_sec = p_parts[0] if len(p_parts) > 0 else "0"
                            print(f"     [{j+1}] {time_sec}s: {comment['m']}")
                        
                        if len(comments) > 3:
                            print(f"     ... 还有 {len(comments) - 3} 条弹幕")
                    else:
                        print("   - 暂无弹幕数据")
                        
                else:
                    print(f"❌ 请求失败: HTTP {response.status_code}")
                    print(f"   错误信息: {response.text}")
                    
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        print("\n🎉 测试完成!")

async def test_format_conversion():
    """测试格式转换功能"""
    print("\n🔄 测试格式转换功能...")
    
    # 模拟JSON格式数据
    json_data = {
        "code": 23,
        "name": "test",
        "danum": 2,
        "danmuku": [
            [0, "top", "#ff00d0", "", "测试弹幕1", "", "", "24px"],
            [61, "right", "#ff006f", "", "测试弹幕2", "", "", "24px"]
        ]
    }
    
    # 模拟XML格式数据
    xml_data = '''<?xml version="1.0" encoding="utf-8"?>
<i>
<d p="0,1,25,16777215,1755835991,0,0,26732601000067074,1">测试XML弹幕1</d>
<d p="30.5,4,25,65280,1755835991,0,0,26732601000067074,1">测试XML弹幕2</d>
</i>'''
    
    # 导入解析函数进行测试
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    
    try:
        from main import parse_json_danmu, parse_xml_danmu, convert_to_dandanplay_format
        
        # 测试JSON解析
        print("📄 测试JSON格式解析...")
        json_comments = parse_json_danmu(json_data)
        print(f"   解析结果: {len(json_comments)} 条弹幕")
        for comment in json_comments:
            print(f"   - {comment['time']}s: {comment['content']}")
        
        # 测试XML解析
        print("\n📄 测试XML格式解析...")
        xml_comments = parse_xml_danmu(xml_data)
        print(f"   解析结果: {len(xml_comments)} 条弹幕")
        for comment in xml_comments:
            print(f"   - {comment['time']}s: {comment['content']}")
        
        # 测试dandanplay格式转换
        print("\n🔄 测试dandanplay格式转换...")
        all_comments = json_comments + xml_comments
        dandan_response = convert_to_dandanplay_format(all_comments)
        
        print(f"   转换结果: {dandan_response.count} 条弹幕")
        for comment in dandan_response.comments:
            print(f"   - cid:{comment.cid}, p:{comment.p}, m:{comment.m}")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
    except Exception as e:
        print(f"❌ 格式转换测试失败: {e}")

def print_usage():
    """打印使用说明"""
    print("""
🎯 弹幕聚合服务测试客户端

使用方法:
1. 确保弹幕聚合服务已启动 (python main.py)
2. 运行测试: python test_client.py

测试内容:
- 服务状态检查
- 健康检查
- 弹幕数据获取
- 格式转换功能

注意事项:
- 服务默认运行在 http://localhost:8080
- 测试使用的是示例URL，实际使用时请替换为真实的视频地址
- 某些弹幕源可能需要特定的视频URL格式
    """)

if __name__ == "__main__":
    print_usage()
    
    # 运行测试
    asyncio.run(test_danmu_service())
    asyncio.run(test_format_conversion())
