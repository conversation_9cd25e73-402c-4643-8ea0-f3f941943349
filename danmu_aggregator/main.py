#!/usr/bin/env python3
"""
弹幕聚合服务
将多个弹幕接口的数据转换为dandanplay兼容格式
"""

import asyncio
import json
import logging
import re
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional
from urllib.parse import quote, unquote

import httpx
from fastapi import FastAPI, Query, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from config import Config

# 配置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="弹幕聚合服务",
    description="将多个弹幕源聚合并转换为dandanplay兼容格式",
    version="1.0.0"
)

class Comment(BaseModel):
    """弹幕模型"""
    cid: int
    p: str  # 格式: "时间,模式,颜色,来源"
    m: str  # 弹幕内容

class CommentResponse(BaseModel):
    """弹幕响应模型"""
    count: int
    comments: List[Comment]

def parse_json_danmu(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析JSON格式弹幕数据
    格式: [时间, 位置, 颜色, "", 内容, "", "", 字体大小]
    """
    comments = []
    if not isinstance(data, dict) or 'danmuku' not in data:
        return comments
    
    danmuku_list = data.get('danmuku', [])
    for i, item in enumerate(danmuku_list):
        if not isinstance(item, list) or len(item) < 5:
            continue
            
        try:
            time_sec = float(item[0])
            position = item[1]  # "top", "right", "bottom"
            color_hex = item[2]  # "#ff00d0"
            content = item[4]
            
            # 转换位置为dandanplay格式
            mode = 1  # 默认滚动
            if position == "top":
                mode = 5  # 顶部
            elif position == "bottom":
                mode = 4  # 底部
            elif position == "right":
                mode = 1  # 滚动
                
            # 转换颜色为RGB数值
            color_rgb = 16777215  # 默认白色
            if color_hex and color_hex.startswith('#'):
                try:
                    color_rgb = int(color_hex[1:], 16)
                except ValueError:
                    pass
            
            comments.append({
                'cid': i + 1,
                'time': time_sec,
                'mode': mode,
                'color': color_rgb,
                'content': content,
                'source': 1
            })
        except (ValueError, IndexError) as e:
            logger.warning(f"解析JSON弹幕项失败: {item}, 错误: {e}")
            continue
    
    return comments

def parse_xml_danmu(xml_content: str) -> List[Dict[str, Any]]:
    """
    解析XML格式弹幕数据
    格式: <d p="时间,模式,字体大小,颜色,时间戳,池,用户ID,弹幕ID,权重">内容</d>
    """
    comments = []
    try:
        root = ET.fromstring(xml_content)
        for i, d_elem in enumerate(root.findall('d')):
            p_attr = d_elem.get('p', '')
            content = d_elem.text or ''
            
            if not p_attr or not content:
                continue
                
            try:
                p_parts = p_attr.split(',')
                if len(p_parts) < 4:
                    continue
                    
                time_sec = float(p_parts[0])
                mode = int(p_parts[1])
                font_size = int(p_parts[2]) if len(p_parts) > 2 else 25
                color = int(p_parts[3]) if len(p_parts) > 3 else 16777215
                
                comments.append({
                    'cid': i + 1,
                    'time': time_sec,
                    'mode': mode,
                    'color': color,
                    'content': content,
                    'source': 2
                })
            except (ValueError, IndexError) as e:
                logger.warning(f"解析XML弹幕项失败: p='{p_attr}', content='{content}', 错误: {e}")
                continue
                
    except ET.ParseError as e:
        logger.error(f"XML解析失败: {e}")
    
    return comments

async def fetch_danmu_from_source(client: httpx.AsyncClient, source_url: str, video_url: str) -> List[Dict[str, Any]]:
    """从单个弹幕源获取弹幕数据"""
    try:
        full_url = source_url + quote(video_url, safe=':/?#[]@!$&\'()*+,;=')
        logger.info(f"请求弹幕源: {full_url}")
        
        response = await client.get(full_url, timeout=10.0)
        response.raise_for_status()
        
        content_type = response.headers.get('content-type', '').lower()
        
        if 'application/json' in content_type or 'text/json' in content_type:
            # JSON格式
            try:
                data = response.json()
                return parse_json_danmu(data)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                return []
        elif 'application/xml' in content_type or 'text/xml' in content_type or response.text.strip().startswith('<?xml'):
            # XML格式
            return parse_xml_danmu(response.text)
        else:
            # 尝试自动检测格式
            text = response.text.strip()
            if text.startswith('<?xml') or text.startswith('<'):
                return parse_xml_danmu(text)
            elif text.startswith('{') or text.startswith('['):
                try:
                    data = json.loads(text)
                    return parse_json_danmu(data)
                except json.JSONDecodeError:
                    pass
            
            logger.warning(f"未知的响应格式: {content_type}")
            return []
            
    except httpx.TimeoutException:
        logger.warning(f"请求超时: {source_url}")
        return []
    except httpx.HTTPStatusError as e:
        logger.warning(f"HTTP错误 {e.response.status_code}: {source_url}")
        return []
    except Exception as e:
        logger.error(f"获取弹幕失败: {source_url}, 错误: {e}")
        return []

async def aggregate_danmu(video_url: str) -> List[Dict[str, Any]]:
    """聚合所有弹幕源的数据"""
    all_comments = []
    
    async with httpx.AsyncClient(
        headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'},
        follow_redirects=True
    ) as client:
        # 并发请求所有弹幕源
        tasks = [
            fetch_danmu_from_source(client, source, video_url)
            for source in DANMU_SOURCES
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"弹幕源 {i+1} 请求异常: {result}")
                continue
            elif isinstance(result, list):
                logger.info(f"弹幕源 {i+1} 获取到 {len(result)} 条弹幕")
                all_comments.extend(result)
    
    # 去重和排序
    unique_comments = {}
    for comment in all_comments:
        # 使用时间+内容作为去重键
        key = f"{comment['time']:.2f}_{comment['content']}"
        if key not in unique_comments:
            unique_comments[key] = comment
    
    # 按时间排序
    sorted_comments = sorted(unique_comments.values(), key=lambda x: x['time'])
    
    logger.info(f"聚合完成: 总计 {len(all_comments)} 条弹幕，去重后 {len(sorted_comments)} 条")
    return sorted_comments

def convert_to_dandanplay_format(comments: List[Dict[str, Any]]) -> CommentResponse:
    """转换为dandanplay格式"""
    dandan_comments = []
    
    for comment in comments:
        # 构建p参数: "时间,模式,颜色,来源"
        p = f"{comment['time']},{comment['mode']},{comment['color']},{comment['source']}"
        
        dandan_comments.append(Comment(
            cid=comment['cid'],
            p=p,
            m=comment['content']
        ))
    
    return CommentResponse(
        count=len(dandan_comments),
        comments=dandan_comments
    )

@app.get("/", summary="服务状态")
async def root():
    """服务状态检查"""
    return {
        "service": "弹幕聚合服务",
        "version": "1.0.0",
        "status": "running",
        "sources": len(DANMU_SOURCES)
    }

@app.get("/danmu", response_model=CommentResponse, summary="获取聚合弹幕")
async def get_danmu(
    url: str = Query(..., description="视频播放地址"),
    chConvert: int = Query(0, description="简繁转换: 0-不转换, 1-转简体, 2-转繁体")
):
    """
    获取聚合弹幕数据，兼容dandanplay格式
    
    参数:
    - url: 视频播放地址
    - chConvert: 简繁转换选项
    
    返回dandanplay兼容的弹幕数据格式
    """
    if not url:
        raise HTTPException(status_code=400, detail="缺少视频URL参数")
    
    try:
        # 聚合弹幕数据
        comments = await aggregate_danmu(url)
        
        # 转换为dandanplay格式
        response = convert_to_dandanplay_format(comments)
        
        # TODO: 实现简繁转换功能
        if chConvert in [1, 2]:
            logger.info(f"简繁转换功能待实现: chConvert={chConvert}")
        
        return response
        
    except Exception as e:
        logger.error(f"获取弹幕失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取弹幕失败: {str(e)}")

@app.get("/health", summary="健康检查")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": asyncio.get_event_loop().time()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
