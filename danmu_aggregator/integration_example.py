#!/usr/bin/env python3
"""
集成示例：如何将弹幕聚合服务集成到主项目中
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from urllib.parse import quote

import httpx

logger = logging.getLogger(__name__)

class DanmuAggregatorClient:
    """弹幕聚合服务客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url.rstrip('/')
        self.client = None
    
    async def __aenter__(self):
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={'User-Agent': 'DanmuAggregator-Client/1.0'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def get_danmu(self, video_url: str, ch_convert: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取聚合弹幕数据
        
        Args:
            video_url: 视频播放地址
            ch_convert: 简繁转换 (0-不转换, 1-简体, 2-繁体)
            
        Returns:
            弹幕数据字典或None
        """
        try:
            encoded_url = quote(video_url, safe='')
            request_url = f"{self.base_url}/danmu?url={encoded_url}&chConvert={ch_convert}"
            
            response = await self.client.get(request_url)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"从聚合服务获取到 {data.get('count', 0)} 条弹幕")
            return data
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP错误: {e.response.status_code} - {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"获取聚合弹幕失败: {e}")
            return None
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            return response.status_code == 200
        except Exception:
            return False

# 主项目集成示例
class AggregatorScraper:
    """
    聚合弹幕抓取器 - 可以添加到主项目的scrapers目录中
    """
    
    def __init__(self, aggregator_url: str = "http://localhost:8080"):
        self.name = "aggregator"
        self.display_name = "弹幕聚合服务"
        self.aggregator_url = aggregator_url
        self.enabled = True
    
    async def search_anime(self, keyword: str) -> List[Dict[str, Any]]:
        """
        搜索动画 - 聚合服务不支持搜索，返回空列表
        主项目可以通过其他方式实现搜索功能
        """
        return []
    
    async def get_episodes(self, anime_id: str) -> List[Dict[str, Any]]:
        """
        获取分集列表 - 聚合服务不支持分集列表，返回空列表
        """
        return []
    
    async def get_comments(self, episode_url: str) -> List[Dict[str, Any]]:
        """
        获取弹幕评论 - 这是聚合服务的核心功能
        
        Args:
            episode_url: 视频播放地址
            
        Returns:
            弹幕列表
        """
        async with DanmuAggregatorClient(self.aggregator_url) as client:
            # 首先检查服务是否可用
            if not await client.health_check():
                logger.warning("弹幕聚合服务不可用")
                return []
            
            # 获取弹幕数据
            danmu_data = await client.get_danmu(episode_url)
            if not danmu_data:
                return []
            
            # 转换为主项目期望的格式
            comments = []
            for comment in danmu_data.get('comments', []):
                # 解析p参数
                p_parts = comment['p'].split(',')
                if len(p_parts) >= 4:
                    try:
                        time_sec = float(p_parts[0])
                        mode = int(p_parts[1])
                        color = int(p_parts[2])
                        source = int(p_parts[3])
                        
                        # 转换为主项目的弹幕格式
                        comments.append({
                            'cid': str(comment['cid']),
                            'p': comment['p'],
                            'm': comment['m'],
                            't': time_sec  # 添加时间字段便于排序
                        })
                    except (ValueError, IndexError):
                        continue
            
            logger.info(f"聚合服务返回 {len(comments)} 条弹幕")
            return comments
    
    def get_source_info(self) -> Dict[str, Any]:
        """获取数据源信息"""
        return {
            'name': self.name,
            'display_name': self.display_name,
            'description': '聚合多个弹幕源的数据',
            'website': self.aggregator_url,
            'enabled': self.enabled,
            'supports_search': False,
            'supports_episodes': False,
            'supports_comments': True
        }

async def integration_demo():
    """集成演示"""
    print("🚀 弹幕聚合服务集成演示")
    
    # 1. 直接使用客户端
    print("\n📡 方式1: 直接使用客户端")
    async with DanmuAggregatorClient() as client:
        # 健康检查
        is_healthy = await client.health_check()
        print(f"服务状态: {'✅ 正常' if is_healthy else '❌ 异常'}")
        
        if is_healthy:
            # 获取弹幕
            test_url = "https://example.com/video.m3u8"
            danmu_data = await client.get_danmu(test_url)
            if danmu_data:
                print(f"获取到 {danmu_data['count']} 条弹幕")
            else:
                print("未获取到弹幕数据")
    
    # 2. 使用抓取器类
    print("\n🔧 方式2: 使用抓取器类")
    scraper = AggregatorScraper()
    
    # 获取源信息
    source_info = scraper.get_source_info()
    print(f"数据源: {source_info['display_name']}")
    print(f"描述: {source_info['description']}")
    
    # 获取弹幕
    test_url = "https://example.com/video.m3u8"
    comments = await scraper.get_comments(test_url)
    print(f"通过抓取器获取到 {len(comments)} 条弹幕")
    
    # 显示前几条弹幕
    for i, comment in enumerate(comments[:3]):
        print(f"  [{i+1}] {comment['t']}s: {comment['m']}")

# 主项目中的使用示例
async def main_project_usage_example():
    """主项目中的使用示例"""
    print("\n🏗️ 主项目集成示例")
    
    # 假设这是主项目中的代码
    scrapers = {
        'bilibili': None,  # 其他抓取器
        'iqiyi': None,
        'aggregator': AggregatorScraper()  # 添加聚合抓取器
    }
    
    # 获取弹幕的统一接口
    async def get_all_comments(video_url: str) -> List[Dict[str, Any]]:
        """从所有可用的抓取器获取弹幕"""
        all_comments = []
        
        # 尝试从聚合服务获取弹幕
        aggregator = scrapers.get('aggregator')
        if aggregator and aggregator.enabled:
            try:
                comments = await aggregator.get_comments(video_url)
                all_comments.extend(comments)
                print(f"✅ 聚合服务: {len(comments)} 条弹幕")
            except Exception as e:
                print(f"❌ 聚合服务失败: {e}")
        
        # 可以继续添加其他抓取器的逻辑...
        
        return all_comments
    
    # 测试统一接口
    test_url = "https://example.com/video.m3u8"
    all_comments = await get_all_comments(test_url)
    print(f"总计获取到 {len(all_comments)} 条弹幕")

if __name__ == "__main__":
    # 运行演示
    asyncio.run(integration_demo())
    asyncio.run(main_project_usage_example())
